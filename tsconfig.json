{
  "compilerOptions": {
    "allowJs": false,
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "jsx": "react-native",
    "module": "es2015",
    "moduleResolution": "node",
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "sourceMap": true,
    "target": "esnext",
    "lib": ["esnext", "dom"],
    "skipLibCheck": true,
    "resolveJsonModule": true,
    "baseUrl": ".",
    "paths": {
      "app/*": ["./app/*"],
      "assets/*": ["./assets/*"]
    }
  },
  "extends": "expo/tsconfig.base",
  "ts-node": {
    "compilerOptions": {
      // compilerOptions specified here will override those declared above,
      // but *only* in ts-node.  Useful if you want ts-node and tsc to use
      // different options with a single tsconfig.json.
      "module": "commonjs"
    }
  },
  "include": ["index.js", "App.tsx", "app", "types", "plugins", "app.config.ts"],
  "exclude": ["node_modules", "test/**/*"]
}
