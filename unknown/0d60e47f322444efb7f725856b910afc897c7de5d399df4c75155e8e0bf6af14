import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree"

/**
 * Auth model that handles caching of user authentication data
 */
export const AuthModel = types
  .model("Auth")
  .props({
    isAuthenticated: types.boolean,
    isRegistering: types.optional(types.boolean, false),
    userId: types.maybe(types.string),
    lastFetchTime: types.maybe(types.number),
  })
  .views((self) => ({
    isCacheValid() {
      if (!self.lastFetchTime || !self.userId) return false
      const CACHE_DURATION = 1000 * 60 * 60 // 1 hour in milliseconds
      return Date.now() - self.lastFetchTime < CACHE_DURATION
    },
    fetchAndCacheUserId() {
      return self.userId
    }
  }))
  .actions((self) => ({
    // Add this new action to update the state
    setUserData(userId: string) {
      self.isAuthenticated = true
      self.isRegistering = false
      self.userId = userId
      self.lastFetchTime = Date.now()
    },

    startRegistration() {
      self.isRegistering = true
      self.isAuthenticated = false
    },

    clearAuth() {
      self.userId = undefined
      self.lastFetchTime = undefined
      self.isAuthenticated = false
      self.isRegistering = false
    },
  }))


export interface Auth extends Instance<typeof AuthModel> {}
export interface AuthSnapshotOut extends SnapshotOut<typeof AuthModel> {}
export interface AuthSnapshotIn extends SnapshotIn<typeof AuthModel> {}
export const createAuthDefaultModel = () => types.optional(AuthModel, {
  userId: undefined,
  lastFetchTime: undefined,
  isAuthenticated: false,
  isRegistering: false,
})
