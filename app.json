{"name": "OneSubMobile", "displayName": "OneSub", "expo": {"name": "OneSubMobile", "slug": "onesub", "scheme": "onesub", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/logo/icon.png", "splash": {"image": "./assets/logo/splash.png", "resizeMode": "contain", "backgroundColor": "#35016f"}, "updates": {"fallbackToCacheTimeout": 0}, "jsEngine": "hermes", "assetBundlePatterns": ["**/*"], "android": {"icon": "./assets/logo/icon.png", "package": "com.onesub.mobile", "adaptiveIcon": {"foregroundImage": "./assets/images/app-icon-android-adaptive-foreground.png", "backgroundImage": "./assets/images/app-icon-android-adaptive-background.png"}, "permissions": ["android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.VIBRATE", "android.permission.WAKE_LOCK"], "googleServicesFile": "./google-services.json"}, "ios": {"icon": "./assets/logo/icon.png", "supportsTablet": true, "bundleIdentifier": "com.onesub.mobile", "googleServicesFile": "./GoogleService-Info.plist", "infoPlist": {"CFBundleURLTypes": [{"CFBundleURLName": "google", "CFBundleURLSchemes": ["1063372141663-35h1h0dofnsgi66esl1c1gc28a4ccfr2"]}]}}, "web": {"favicon": "./assets/logo/favicon.png", "bundler": "metro"}, "plugins": ["expo-localization", "expo-splash-screen", ["react-native-email-link"], ["@react-native-google-signin/google-signin"], ["expo-notifications", {"icon": "./assets/logo/icon.png", "color": "#35016f", "defaultChannel": "other"}], ["expo-build-properties", {"ios": {"newArchEnabled": false}, "android": {"newArchEnabled": false}}], "expo-font", ["@sentry/react-native/expo", {"url": "https://sentry.io/", "project": "react-native", "organization": "onesub"}]], "experiments": {"tsconfigPaths": true}, "extra": {"eas": {"projectId": "d7f916e3-23fe-42eb-8bbb-44ba2dc5170d"}}, "owner": "eskandm"}, "ignite": {"version": "9.10.1"}}