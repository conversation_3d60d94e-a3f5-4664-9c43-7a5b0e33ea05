---
destinationDir: app/navigators
patch:
  path: "app/navigators/index.ts"
  append: "export * from \"./<%= props.pascalCaseName %>Navigator\"\n"
  skip: <%= props.skipIndexFile %>
---
import React from "react"
import { createNativeStackNavigator } from "@react-navigation/native-stack"
import {
  WelcomeScreen
} from "app/screens"

export type <%= props.pascalCaseName %>NavigatorParamList = {
  Demo: undefined
}

const Stack = createNativeStackNavigator<<%= props.pascalCaseName %>NavigatorParamList>()
export const <%= props.pascalCaseName %>Navigator = () => {
  return (
    <Stack.Navigator screenOptions={{ cardStyle: { backgroundColor: "transparent" }, headerShown: false, }}>
      <Stack.Screen name="Demo" component={WelcomeScreen} />
    </Stack.Navigator>
  )
}
