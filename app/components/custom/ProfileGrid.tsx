import * as React from "react"
import { Dimensions, Pressable, StyleProp, View, ViewStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { Text } from "app/components/Text"
import { ListView, ProfileIcon } from "app/components"
import { UserProfile } from "app/models"
import { navigate } from "app/navigators"

export interface ProfileGridProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: StyleProp<ViewStyle>
  data: UserProfile[]
}

const numColumns = 3;
const screenWidth = Dimensions.get('window').width;

export const ProfileGrid = observer(function ProfileGrid(props: ProfileGridProps) {
  const {data} = props;
  const renderItem = ({item}: {item: UserProfile}) => {
    return(
    <Pressable key={item.id} style={$itemContainer} onPress={() => handlePress(item)}>
      <ProfileIcon user={item} />
    </Pressable>
  );
  }

  const handlePress = (item: UserProfile) => {
    navigate("UserProfile", {userId: item.id})
  };

  if(!data || data.length === 0 || data[0].firstName === undefined){
    return (
      <View style={$container}>
        <Text>Loading</Text>
      </View>
      )
  }
  return (
    <View style={$container}>
      <ListView
        estimatedListSize={{height:95, width:100}}
        data={data}
        renderItem={renderItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={numColumns}
        estimatedItemSize={200}
      />
    </View>
  );
})
const $container: ViewStyle = {
  flex:1
}
const $itemContainer: ViewStyle = {
    margin: 4,
    justifyContent: 'center',
    alignItems: 'center',
    width: (screenWidth - 60) / numColumns, // Adjust width for 4 columns
}
