import * as React from "react"
import { ImageStyle, View, ViewStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { Text } from "app/components/Text"
import { AutoImage, ListItem } from "app/components"

export interface NotificationListItemProps {
  title: string,
  description: string,
  sponsored: boolean,
  logo: string
}

/**
 * Describe your component here
 */
export const NotificationListItem = observer(function NotificationListItem(props: NotificationListItemProps) {


  return (
    <ListItem
      LeftComponent={<AutoImage style={$companyLogoNotificationImage} maxHeight={40} source={{uri:props.logo}}/>}
      style={$hiListItem}
      topSeparator={true}
      bottomSeparator={true}
      height={56}>
      <View style={{paddingTop:8, flexDirection:"column",alignSelf:"center"}}>
        <Text weight={"bold"}>{props.title}
          {props.sponsored && <Text size={"xxs"} weight={"light"}> Sponsored</Text>}
        </Text>
        <Text size={"xxs"} weight={"light"}>{props.description}</Text>
      </View>
    </ListItem>
  )
})

const $hiListItem: ViewStyle = {
  padding:2,
}
const $companyLogoNotificationImage: ImageStyle = {
  alignSelf:"center",
  marginRight:16
}
