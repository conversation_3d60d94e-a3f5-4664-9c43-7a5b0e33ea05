import React from "react"

import { View,ViewStyle, ImageStyle, TextStyle } from "react-native"
import { Product } from "app/models"
import { colors, spacing } from "app/theme"
import { AutoImage, Text } from "app/components"

type Props = {
  product: Product
}

export const ProductHeader = ({ product }: Props) => (
  <View style={$header}>
    <AutoImage
      source={{ uri: product.merchant?.merchantGroup?.logo?.url }}
      style={$image}
      // defaultSource={require("../../assets/images/placeholder.png")}
    />
    <Text preset="heading" text={product.name} style={$title} />
    <Text preset="subheading" text={product.description} style={$description} />
  </View>
)

const $header: ViewStyle = {
  alignItems: 'center',
  marginBottom: spacing.xxl,
}

const $image: ImageStyle = {
  height: 150,
  width: 150,
  borderRadius: 16,
  marginBottom: spacing.lg,
  backgroundColor: colors.palette.neutral100,
}

const $title: TextStyle = {
  marginBottom: spacing.xs,
  textAlign: 'center',
  fontSize: 24,
}

const $description: TextStyle = {
  marginBottom: spacing.lg,
  textAlign: 'center',
  color: colors.textDim,
  lineHeight: 20,
}
