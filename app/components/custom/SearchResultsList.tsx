import React from "react"
import { Image, StyleSheet, View } from "react-native"
import { observer } from "mobx-react-lite"
import { Text } from "app/components/Text"

export interface SearchResultsListProps {
  data: Array<any>
}

export const SearchResultsList = observer(function SearchResultsList(props: SearchResultsListProps) {
  const { data } = props

  if (!data?.length) return null

  return (
    <View style={styles.container}>
      {data.map((item, index) => {
        const title = item?.name ?? item?.name ?? "Untitled"
        const description = item.entity?.description
        const logo = item?.logo?.url

        return (
          <View key={index} style={styles.card}>
            {logo ? <Image source={{ uri: logo }} style={styles.logo} /> : null}
            <View style={styles.textContainer}>
              <Text preset="bold" style={styles.title}>{title}</Text>
              {description ? <Text style={styles.description}>{description}</Text> : null}
            </View>
          </View>
        )
      })}
    </View>
  )
})

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#f9f9f9",
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
    padding: 8,
  },
  card: {
    flexDirection: "row",
    paddingVertical: 12,
    borderBottomColor: "#eee",
    borderBottomWidth: 1,
  },
  logo: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
    justifyContent: "center",
  },
  title: {
    fontSize: 16,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: "#555",
  },
})
