import React from "react"
import { Pressable, ViewStyle, TextStyle, View } from "react-native"
import { PricingTier } from "app/models"
import { Text } from "app/components"
import { colors, spacing } from "app/theme"


type Props = {
  tier: PricingTier
  isSelected: boolean
  onPress: () => void
}

export const PricingTierCard = ({ tier, isSelected, onPress }: Props) => (
  <Pressable
    onPress={onPress}
    style={({ pressed }) => [
      $tierCard,
      pressed && $tierCardPressed,
      isSelected && $tierCardSelected,
    ]}
  >
    <View style={$tierHeader}>
      <Text preset="bold" text={tier.name} style={$tierName} />
      <Text preset="heading" text={`€${tier.cost}`} style={$tierPrice} />
    </View>
    {tier.description && (
      <Text text={tier.description} style={$tierDescription} />
    )}
    {isSelected && (
      <View style={$selectedBadge}>
        <Text text="Selected" preset="bold" style={$badgeText} />
      </View>
    )}
  </Pressable>
)

const $tierCard: ViewStyle = {
  padding: spacing.lg,
  borderWidth: 1,
  borderColor: colors.border,
  borderRadius: 12,
  marginBottom: spacing.md,
  backgroundColor: colors.background,
  position: 'relative',
  overflow: 'hidden',
}

const $tierCardPressed: ViewStyle = {
  transform: [{ scale: 0.98 }],
  opacity: 0.9,
}

const $tierCardSelected: ViewStyle = {
  borderColor: colors.palette.primary600,
  backgroundColor: colors.palette.primary100,
  borderWidth: 2,
}

const $tierHeader: ViewStyle = {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: spacing.xs,
}

const $tierName: TextStyle = {
  fontSize: 16,
  color: colors.text,
}

const $tierPrice: TextStyle = {
  fontSize: 18,
  color: colors.palette.primary600,
}

const $tierDescription: TextStyle = {
  color: colors.textDim,
  fontSize: 14,
  lineHeight: 20,
}

const $selectedBadge: ViewStyle = {
  position: 'absolute',
  top: -1,
  right: -1,
  backgroundColor: colors.palette.primary600,
  paddingVertical: spacing.xxs,
  paddingHorizontal: spacing.sm,
  borderBottomLeftRadius: 8,
  borderTopRightRadius: 11,
}

const $badgeText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 12,
}
