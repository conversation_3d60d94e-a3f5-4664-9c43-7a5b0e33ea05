import * as React from "react"
import { Dimensions, ImageStyle, Pressable, StyleProp, TextStyle, View, ViewStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { Text } from "app/components/Text"
import { AutoImage, ListView } from "app/components"
import { navigate } from "app/navigators"

export interface ActiveSubscriptionsGridProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: StyleProp<ViewStyle>
  data: any[]
}

const numColumns = 3;
const screenWidth = Dimensions.get('window').width;

export const ActiveSubscriptionsGrid = observer(function ActiveSubscriptionsGrid(props: ActiveSubscriptionsGridProps) {


  const subsBySections = props.data.reduce((acc, item) => {
    const section = item.category
    if (!acc[section]) {
      acc[section] = [];
    }
    acc[section].push(item);
    return acc;
  }, {});
  // @ts-ignore
  const renderItem = ({ item }) => (
    <Pressable style={$itemContainer} onPress={() => handlePress(item)}>
      <AutoImage source={{ uri: item.imageUrl }} style={$logo} />
      <Text style={$title}>{item.title}</Text>
    </Pressable>
  );

  const handlePress = (item: unknown) => {
    navigate('Product', item)
  };

  return (
    <View style={$container}>
      {Object.keys(subsBySections).map((section) => (
        <View style={{width:100}} key={section}>
          <Text preset={"bold"} style={{textAlign:"center"}}>{section}</Text>
          <ListView
            estimatedListSize={{ height: 95, width: 100 }}
            data={subsBySections[section]}
            renderItem={renderItem}
            keyExtractor={(item) => item.id.toString()}
            numColumns={numColumns}
            estimatedItemSize={200}
          />
        </View>
      ))}
    </View>
  );
})
const $container: ViewStyle = {
  flex:1,
  flexDirection: 'row',
  flexWrap: 'wrap',
  justifyContent: 'space-around',
}
const $itemContainer: ViewStyle = {
    margin: 4,
    justifyContent: 'center',
    alignItems: 'center',
    width: (screenWidth - 60) / numColumns, // Adjust width for 3 columns
}
const $logo: ImageStyle = {
  width: 40,
    height: 40,
    resizeMode: 'contain',
    marginBottom: 8,
}
const $title: TextStyle = {
  fontSize: 14,
  textAlign: 'center',
}
