import * as React from "react"
import { observer } from "mobx-react-lite"
import { Text } from "app/components/Text"
import { AutoImage } from "app/components"
import { UserProfile } from "app/models"
import { colors } from "app/theme"
import { ImageStyle, TextStyle, View } from "react-native"

export interface ProfileIconProps {
  user: UserProfile,
  fullName?: boolean
}

/**
 * Describe your component here
 */
export const ProfileIcon = observer(function ProfileIcon(props: ProfileIconProps) {
  const {user, fullName} = props

  const avatarUrl = user.avatarUrl && user.avatarUrl.length > 0 ? user.avatarUrl : `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=${colors.palette.primary200.substring(1)}`
  return (
    <View style={{alignItems:"center"}}>
      <AutoImage source={{ uri: avatarUrl }} style={$logo} />
      <View style={{flexDirection:"row", gap:2}}>
        <Text style={$title}>{user.firstName}</Text>
      {fullName && <Text style={$title}>{user.lastName}</Text>}
      </View>
    </View>
  )
})

const $logo: ImageStyle = {
  width: 40,
  height: 40,
  resizeMode: 'contain',
  marginBottom: 8,
}
const $title: TextStyle = {
  fontSize: 14,
  textAlign: 'center',
}


