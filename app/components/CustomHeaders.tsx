import * as React from "react"
import { Pressable, StyleProp, View, ViewStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { Header } from "app/components/Header"
import { faArrowLeft, faSearch, faUser } from "@fortawesome/free-solid-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faCreditCard } from "@fortawesome/free-regular-svg-icons"
import { TextField } from "app/components/TextField"
import { useNavigation } from "@react-navigation/native"
import { useEffect, useMemo, useState } from "react"
import { goBack } from "app/navigators"
import { useStores } from "app/models"
import { SearchResultsList } from "app/components/custom/SearchResultsList"
import {supabase} from "app/config/config.base";

export interface CustomHeadersProps {
  style?: StyleProp<ViewStyle>
}

export const CustomHeaders = observer(function CustomHeaders(props: CustomHeadersProps) {
  const navigator = useNavigation()
  const canGoBack = useMemo(() => navigator.canGoBack(), [navigator.getId()])
  const [query, setQuery] = useState("")
  const [results, setResults] = useState([])

  /* TODO: This is very unsafe for production use as it does not debounce requests
  *   Need to create a custom hook to debounce before querying API */
  useEffect(() => {
    const fetchData = async () => {
      if (query.length >= 2){
        const { data, error } = await supabase.rpc('search_entities', { search_term: query, result_limit:10 })
        if(!error){
          const flattenedData = data
              .map( (item:any) => item.data);
          setResults(flattenedData)
        }else{
          console.error(error)
        }
      }
    }
    fetchData()
  }, [query])

  const leftAction = (
    <>
      {canGoBack && (
        <Pressable style={$backButton} onPress={() => goBack()}>
          <FontAwesomeIcon icon={faArrowLeft} />
        </Pressable>
      )}
      <TextField
        onChangeText={setQuery}
        containerStyle={$searchContainer}
        style={$searchBox}
        placeholderTx="navigator.searchPlaceholder"
        LeftAccessory={() => (
          <FontAwesomeIcon color="gray" style={$searchIcon} icon={faSearch} />
        )}
      />
    </>
  )



  return (
    <>
      <Header style={$header} LeftActionComponent={leftAction} />
      <SearchResultsList data={results} />
    </>
  )
})

const $header: ViewStyle = {
  backgroundColor: "white",
  paddingVertical: 8,
}

const $backButton: ViewStyle = {
  marginLeft: 8,
  justifyContent: "center",
}

const $searchContainer: ViewStyle = {
  margin: 16,
  flex: 4,
}

const $searchBox: ViewStyle = {
  margin: 8,
}

const $searchIcon: ViewStyle = {
  margin: 4,
  alignSelf: "center",
}

const $iconContainer: ViewStyle = {
  flex: 1,
  flexDirection: "row",
  marginLeft: 8,
  marginRight: 16,
  gap: 4,
  justifyContent: "flex-end",
  alignItems: "center",
}

const $iconButton: ViewStyle = {
  marginHorizontal: 2,
  padding: 6,
  borderRadius: 16,
  borderWidth: 0.25,
  justifyContent: "center",
  alignItems: "center",
}
