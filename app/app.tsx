/* eslint-disable import/first */

import OneSubOnboarding from "app/screens/Onboarding/OneSubOnboarding"

if (__DEV__) {
  require("./devtools/ReactotronConfig.ts")
}

import "./utils/gestureHandler"
import "./i18n"
import "./utils/ignoreWarnings"
import { useFonts } from "expo-font"
import React from "react"
import { initialWindowMetrics, SafeAreaProvider } from "react-native-safe-area-context"
import * as Linking from "expo-linking"
import {useInitialRootStore, useStores} from "./models"
import { AppNavigator, useNavigationPersistence, navigationRef } from "./navigators"
import { ErrorBoundary } from "./screens/ErrorScreen/ErrorBoundary"
import * as storage from "./utils/storage"
import { customFontsToLoad } from "./theme"
import Config from "./config"
import { AuthProvider } from "app/contexts/AuthContext"
import * as Sentry from "@sentry/react-native"
import { initCrashReporting } from "app/utils/crashReporting"
import { AuthenticationGuardScreen } from "app/screens"
import { NavigationContainer, DarkTheme, DefaultTheme } from "@react-navigation/native"
import { useColorScheme } from "react-native"

export const NAVIGATION_PERSISTENCE_KEY = "NAVIGATION_STATE"

const prefix = Linking.createURL("/")
const config = {
  screens: {
    AuthenticationScreen: {
      path: "auth/magic",
    },
    Welcome: "welcome",
    Demo: {
      screens: {
        DemoShowroom: {
          path: "showroom/:queryIndex?/:itemIndex?",
        },
        DemoDebug: "debug",
        DemoPodcastList: "podcast",
        DemoCommunity: "community",
      },
    },
  },
}

initCrashReporting()

interface AppProps {
  hideSplashScreen: () => Promise<boolean>
}

function App(props: AppProps) {
  const { hideSplashScreen } = props
  const colorScheme = useColorScheme()

  const {
    initialNavigationState,
    onNavigationStateChange,
    isRestored: isNavigationStateRestored,
  } = useNavigationPersistence(storage, NAVIGATION_PERSISTENCE_KEY)

  const [areFontsLoaded, fontLoadError] = useFonts(customFontsToLoad)

  const { rehydrated } = useInitialRootStore(() => {
    setTimeout(hideSplashScreen, 500)
  })

  if (!rehydrated || !isNavigationStateRestored || (!areFontsLoaded && !fontLoadError)) {
    return null
  }

  const linking = {
    prefixes: [prefix],
    config,
  }

  return (
    <SafeAreaProvider initialMetrics={initialWindowMetrics}>
      <ErrorBoundary catchErrors={Config.catchErrors}>
        <AuthProvider>
          <AuthenticationGuardScreen>
            <OneSubOnboarding>
              <NavigationContainer
                ref={navigationRef}
                linking={linking}
                theme={colorScheme === "dark" ? DarkTheme : DefaultTheme}
                initialState={initialNavigationState}
                onStateChange={onNavigationStateChange}
              >
                <AppNavigator />
              </NavigationContainer>
            </OneSubOnboarding>
          </AuthenticationGuardScreen>
        </AuthProvider>
      </ErrorBoundary>
    </SafeAreaProvider>
  )
}

// @ts-ignore
export default Sentry.wrap(App)
