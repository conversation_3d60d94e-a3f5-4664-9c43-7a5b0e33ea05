import React, {createContext, useContext, useEffect, useState} from 'react'
import * as Linking from "expo-linking";
import * as Device from 'expo-device'
import {createSessionFromUrl} from "app/utils/authUtils";
import {Session} from "@supabase/supabase-js";
import {useStores} from "app/models";
import { AppState} from "react-native";
import {supabase} from "app/config/config.base";
import {ErrorType, ErrorExperience, reportSentryError} from "app/utils/crashReporting";
import {authProviderService} from "app/services/AuthProviderService";

interface AuthContextType {
  userId: string | undefined
  authLoading: boolean
  notificationSetup: boolean
}

const AuthContext = createContext<AuthContextType>({
  userId: undefined,
  authLoading: true,
  notificationSetup: false
})

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null)
  const [notificationSetup, setNotificationSetup] = useState<boolean>(false)

  const rootStore = useStores()

  // Deep links created from AuthOptions login flows are handled here
  const url = Linking.useURL();

  useEffect(() => {
    if (url) {
      createSessionFromUrl(url)
        .then(r => {
          if (r) {
            setSession(r)
          }
        })
    }
  }, [url]);

  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);
  useEffect(() => {
    const subscription = AppState.addEventListener("change", (state) => {
      if (state === "active") {
        supabase.auth.startAutoRefresh()
      } else {
        supabase.auth.stopAutoRefresh();
      }
    });

    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    if (session){
      rootStore.auth.setUserData(session?.user.id)
    }
    else{
      rootStore.auth.clearAuth()
      // Reset notification setup when user logs out
      setNotificationSetup(false)
      rootStore.notifications.reset()
    }
  }, [session]);

  // Initialize auth provider service on app startup
  useEffect(() => {
    const initializeAuthProviders = async () => {
      try {
        console.log('Initializing auth provider service...')
        await authProviderService.initialize()
        console.log('Auth provider service initialized successfully')
      } catch (error) {
        console.error('Error initializing auth provider service:', error)
        // Don't throw error to prevent app crash - fallback will be used
      }
    }

    initializeAuthProviders()
  }, []);

  useEffect(() => {
    const initializeNotifications = async () => {
      if (!session?.user?.id || !Device.isDevice) {
        if (__DEV__ && !Device.isDevice && console.tron) {
          console.tron.warn('🔔 Skipping notification setup: Not a physical device')
        }
        return
      }

      // Skip if already setup
      if (notificationSetup) {
        return
      }

      try {
        if (__DEV__ && console.tron) {
          console.tron.log('🔔 Initializing notifications for user:', session.user.id)
        }

        // Check if notifications store is available
        if (!rootStore.notifications) {
          if (__DEV__ && console.tron) {
            console.tron.warn('⚠️ Notifications store not available, skipping initialization')
          }
          return
        }

        // Initialize notification service and register push token
        const success = await rootStore.notifications.initialize(session.user.id)

        if (success) {
          // Register push token with device info
          const deviceInfo = {
            device_name: Device.deviceName || 'Unknown Device',
            os_version: Device.osVersion || 'Unknown',
            device_model: Device.modelName || 'Unknown',
            app_version: '1.0.0' // You can get this from Constants.expoConfig.version
          }

          await rootStore.notifications.registerPushToken(session.user.id, deviceInfo)
          setNotificationSetup(true)

          if (__DEV__ && console.tron) {
            console.tron.log('✅ Notifications initialized successfully')
          }
        } else {
          if (__DEV__ && console.tron) {
            console.tron.warn('⚠️ Notification initialization failed:', rootStore.notifications.lastError)
          }
        }
      } catch (error) {
        // Only report to Sentry if it's available
        try {
          const notificationError = error instanceof Error ? error : new Error('Failed to initialize notifications')
          reportSentryError(notificationError, ErrorType.HANDLED, ErrorExperience.Notifications)
        } catch (sentryError) {
          // Ignore Sentry errors during notification setup
        }

        if (__DEV__ && console.tron) {
          console.tron.error('❌ Notification setup error:', error)
        }
      }
    }

    // Delay initialization slightly to ensure auth is fully settled
    if (session?.user?.id) {
      const timeoutId = setTimeout(initializeNotifications, 2000) // Increased delay
      return () => clearTimeout(timeoutId)
    }
  }, [session?.user?.id, notificationSetup]);



  return (
    <AuthContext.Provider value={{
      userId: session?.user.id,
      authLoading: false,
      notificationSetup
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => useContext(AuthContext)
