import { supabase } from "app/config/config.base"

export const fetchMerchantGroups = async () => {
  const { data, error } = await supabase
    .from("merchant_groups")
    .select("*, logos(url)")
    .order("name", { ascending: true });

  if (error) {
    console.error("Error fetching merchant groups:", error);
    return [];
  }

  // Transform the data to match the expected format
  return data.map(group => ({
    ...group,
    logo: group.logos && group.logos.length > 0 ? group.logos[0] : null
  }));
};

export const fetchMerchants = async (merchantGroupId?: string) => {
  let query = supabase
    .from("merchants")
    .select("*")
    .order("name", { ascending: true });

  // Apply filter if merchantGroupId is provided
  if (merchantGroupId) {
    query = query.eq("merchant_group_id", merchantGroupId);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Error fetching merchants:", error);
    return [];
  }

  return data;
};

export const fetchProducts = async (merchantId?: string) => {
  if (merchantId) {
    const { data, error } = await supabase
      .from("merchants")
        .select("id, products(*)")
        .filter("id", "eq", merchantId);
    if (error) {
      console.error("Error fetching products for merchant:", error);
      return [];
    }
    return data[0].products
  } else {
    // If no merchantId is provided, fetch all products
    const { data, error } = await supabase
      .from("products")
      .select("*")
      .order("name", { ascending: true });

    if (error) {
      console.error("Error fetching products:", error);
      return [];
    }

    return data;
  }
};

export const fetchPricingTiers = async (productId?: string) => {
  const query = supabase
    .from("pricing_tiers")
    .select("*")
      .eq("product_id", productId)
    .order("cost", { ascending: true });

  const { data, error } = await query;

  if (error) {
    console.error("Error fetching pricing tiers:", error);
    return [];
  }

  return data;
};
