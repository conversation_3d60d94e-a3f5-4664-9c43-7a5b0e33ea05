import { AuthProviderService } from '../AuthProviderService'

// Mock dependencies
jest.mock('app/config/config.base', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        data: [
          { provider: 'google', client_id: '1063372141663-35h1h0dofnsgi66esl1c1gc28a4ccfr2.apps.googleusercontent.com' }
        ],
        error: null
      }))
    }))
  }
}))

jest.mock('app/utils/storage', () => ({
  load: jest.fn(() => Promise.resolve(null)),
  save: jest.fn(() => Promise.resolve(true))
}))

describe('AuthProviderService', () => {
  let service: AuthProviderService

  beforeEach(() => {
    service = AuthProviderService.getInstance()
    // Clear any cached data
    service['configCache'].clear()
    service['lastFetchTime'] = null
  })

  it('should fetch Google client ID from Supabase', async () => {
    const clientId = await service.getGoogleClientId()
    expect(clientId).toBe('1063372141663-35h1h0dofnsgi66esl1c1gc28a4ccfr2.apps.googleusercontent.com')
  })

  it('should return null for unknown provider', async () => {
    const clientId = await service.getClientId('unknown')
    expect(clientId).toBeNull()
  })

  it('should cache client IDs in memory', async () => {
    // First call should fetch from Supabase
    const clientId1 = await service.getGoogleClientId()
    
    // Second call should use cache
    const clientId2 = await service.getGoogleClientId()
    
    expect(clientId1).toBe(clientId2)
    expect(clientId1).toBe('1063372141663-35h1h0dofnsgi66esl1c1gc28a4ccfr2.apps.googleusercontent.com')
  })
})
