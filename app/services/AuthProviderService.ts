import { supabase } from "app/config/config.base"
import * as storage from "app/utils/storage"

export interface AuthProviderConfig {
  provider: string
  client_id: string
}

const CACHE_KEY = "auth_provider_config"
const CACHE_DURATION = 1000 * 60 * 60 * 24 // 24 hours

/**
 * Service to fetch and cache authentication provider configurations from Supabase
 */
export class AuthProviderService {
  private static instance: AuthProviderService
  private configCache: Map<string, AuthProviderConfig> = new Map()
  private lastFetchTime: number | null = null

  static getInstance(): AuthProviderService {
    if (!AuthProviderService.instance) {
      AuthProviderService.instance = new AuthProviderService()
    }
    return AuthProviderService.instance
  }

  /**
   * Get the client ID for a specific provider
   */
  async getClientId(provider: string): Promise<string | null> {
    try {
      // Check memory cache first
      if (this.configCache.has(provider)) {
        return this.configCache.get(provider)!.client_id
      }

      // Check if we need to refresh cache
      if (this.isCacheValid()) {
        await this.loadFromStorage()
        if (this.configCache.has(provider)) {
          return this.configCache.get(provider)!.client_id
        }
      }

      // Fetch from Supabase
      await this.fetchFromSupabase()
      
      if (this.configCache.has(provider)) {
        return this.configCache.get(provider)!.client_id
      }

      console.warn(`No client ID found for provider: ${provider}`)
      return null
    } catch (error) {
      console.error(`Error getting client ID for ${provider}:`, error)
      return null
    }
  }

  /**
   * Get Google client ID specifically
   */
  async getGoogleClientId(): Promise<string | null> {
    return this.getClientId('google')
  }

  /**
   * Fetch all auth provider configs from Supabase
   */
  private async fetchFromSupabase(): Promise<void> {
    try {
      console.log('Fetching auth provider configs from Supabase...')
      
      const { data, error } = await supabase
        .from('auth_provider_config')
        .select('provider, client_id')

      if (error) {
        throw error
      }

      if (data && data.length > 0) {
        // Update memory cache
        this.configCache.clear()
        data.forEach((config: AuthProviderConfig) => {
          this.configCache.set(config.provider, config)
        })

        // Save to storage
        await this.saveToStorage()
        this.lastFetchTime = Date.now()
        
        console.log(`Successfully fetched ${data.length} auth provider configs`)
      } else {
        console.warn('No auth provider configs found in Supabase')
      }
    } catch (error) {
      console.error('Error fetching auth provider configs from Supabase:', error)
      throw error
    }
  }

  /**
   * Load cached configs from AsyncStorage
   */
  private async loadFromStorage(): Promise<void> {
    try {
      const cached = await storage.load(CACHE_KEY)
      if (cached && typeof cached === 'object') {
        const { configs, timestamp } = cached as { configs: AuthProviderConfig[], timestamp: number }
        
        if (configs && Array.isArray(configs)) {
          this.configCache.clear()
          configs.forEach((config: AuthProviderConfig) => {
            this.configCache.set(config.provider, config)
          })
          this.lastFetchTime = timestamp
          console.log(`Loaded ${configs.length} auth provider configs from cache`)
        }
      }
    } catch (error) {
      console.error('Error loading auth provider configs from storage:', error)
    }
  }

  /**
   * Save configs to AsyncStorage
   */
  private async saveToStorage(): Promise<void> {
    try {
      const configs = Array.from(this.configCache.values())
      await storage.save(CACHE_KEY, {
        configs,
        timestamp: this.lastFetchTime
      })
    } catch (error) {
      console.error('Error saving auth provider configs to storage:', error)
    }
  }

  /**
   * Check if cache is still valid
   */
  private isCacheValid(): boolean {
    if (!this.lastFetchTime) return false
    return Date.now() - this.lastFetchTime < CACHE_DURATION
  }

  /**
   * Force refresh the cache
   */
  async refreshCache(): Promise<void> {
    this.lastFetchTime = null
    this.configCache.clear()
    await this.fetchFromSupabase()
  }

  /**
   * Initialize the service by loading from cache
   */
  async initialize(): Promise<void> {
    try {
      await this.loadFromStorage()
      
      // If cache is invalid or empty, fetch from Supabase
      if (!this.isCacheValid() || this.configCache.size === 0) {
        await this.fetchFromSupabase()
      }
    } catch (error) {
      console.error('Error initializing AuthProviderService:', error)
    }
  }
}

// Export singleton instance
export const authProviderService = AuthProviderService.getInstance()
