import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree";
import { withSetPropAction } from "../helpers/withSetPropAction";
import { ProductModel } from "app/models"

/**
 * Define the LogoModel
 */
export const LogoModel = types
  .model("Logo")
  .props({
    url: types.string,
    width: types.maybeNull(types.number),
    height: types.maybeNull(types.number),
  })
  .actions(withSetPropAction);



/**
 * Define the MerchantModel
 */
// @ts-ignore
export const MerchantModel =
  types
    .model("Merchant")
    .props({
      id: types.identifier,
      name: types.maybeNull(types.string),
      categories: types.maybeNull(types.array(types.string)),
      location: types.maybeNull(
        types.model({
          latitude: types.number,
          longitude: types.number,
          address: types.maybeNull(types.string),
          placeId: types.maybeNull(types.string),
        })
      ),
      merchantGroup: types.maybeNull(types.safeReference(types.late(() => MerchantGroupModel))),
      region: types.maybeNull(
        types.model({
          id: types.identifier,
          name: types.string,
          code: types.string,
        })
      ),
      subscriptionProductIds: types.array(types.safeReference(types.late(()=>ProductModel))),
      updatedAt: types.string,
    })
    .actions(withSetPropAction)
    .actions((self) => ({
      setMerchantGroup(merchantGroup: Instance<typeof MerchantGroupModel>) {
        self.merchantGroup = merchantGroup;
      },
      addSubscriptionProduct(productId: string) {
        self.subscriptionProductIds.push(productId);
      },
    }));

/**
 * Define the MerchantGroupModel
 */
// @ts-ignore
export const MerchantGroupModel =
  types
    .model("MerchantGroup")
    .props({
      id: types.identifier,
      name: types.string,
      onlineOnly: types.boolean,
      description: types.maybeNull(types.string),
      categories: types.array(types.string),
      logo: types.maybeNull(LogoModel),
      merchants: types.optional(types.array(types.safeReference(types.late(() => MerchantModel))), []),
      updatedAt: types.string,
    })
    .actions(withSetPropAction)
    .views((self) => ({
      merchantCount: () => self.merchants.length,
      hasLogo: () => !!self.logo,
    }))
    .actions((self) => ({
      addMerchant(merchant: Instance<typeof MerchantModel>) {
        if (!self.merchants.includes(merchant)) {
          self.merchants.push(merchant);
        }
      },
      removeMerchantById(merchantId: string) {
        const index = self.merchants.findIndex((m) => m?.id === merchantId);
        if (index !== -1) {
          self.merchants.splice(index, 1); // Remove the merchant at the found index
        }
      },
    }));


/**
 * Interfaces for Strong Typing
 */

export interface Logo extends Instance<typeof LogoModel> {}
export interface LogoSnapshotOut extends SnapshotOut<typeof LogoModel> {}
export interface LogoSnapshotIn extends SnapshotIn<typeof LogoModel> {}

export interface Merchant extends Instance<typeof MerchantModel> {}
export interface MerchantSnapshotOut extends SnapshotOut<typeof MerchantModel> {}
export interface MerchantSnapshotIn extends SnapshotIn<typeof MerchantModel> {}

export interface MerchantGroup extends Instance<typeof MerchantGroupModel> {}
export interface MerchantGroupSnapshotOut extends SnapshotOut<typeof MerchantGroupModel> {}
export interface MerchantGroupSnapshotIn extends SnapshotIn<typeof MerchantGroupModel> {}
