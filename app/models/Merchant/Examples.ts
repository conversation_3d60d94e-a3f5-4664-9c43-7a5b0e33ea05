import { ProfileQuestionModel, LogoModel, MerchantGroupModel, MerchantModel, ProductModel } from "app/models"

export const createPendingQuestionForUser = (userId: string) =>
{
  return ProfileQuestionModel.create({
    userOrHousehold: "user",
    targetId:userId,
    questionId: "ABC",
    questionText: "Are you a student?",
    allowMultiSelect: false,
    options: [{
      "answerText": "Yes",
      "grantsDescriptors": [`student`]
    },
      {
        "answerText": "No",
        "grantsDescriptors": []
      },
    ]
  })
}




const FlyeFitMerchantGroup = MerchantGroupModel.create({
  id: "mg-123",
  name: "FlyeFit",
  description: "The flexible gym",
  onlineOnly: false,
  logo: LogoModel.create({
    url: "https://pbs.twimg.com/profile_images/1410622449977376768/1oCQ6v2l_400x400.jpg",
  }),
  updatedAt: new Date().toISOString(),
});

const SpotifyMerchantGroup = MerchantGroupModel.create({
  id: "mg-234",
  name: "Spotify",
  onlineOnly: true,
  logo: LogoModel.create({
    url: "https://m.media-amazon.com/images/I/51rttY7a+9L.png",
  }),
  updatedAt: new Date().toISOString(),
});

const AertricityMerchantGroup = MerchantGroupModel.create({
  id: "mg-345",
  name: "Aertricity",
  onlineOnly: false,
  logo: LogoModel.create({
    url: "https://play-lh.googleusercontent.com/z0-xrFvgLOFWhdVS_-CKF1_QnfccRCJVXr-aNRVGFz9xzOSLeWjcVs7f9YCgjOQ2P84",
  }),
  updatedAt: new Date().toISOString(),
});

// Merchants
const SpotifyMerchant = MerchantModel.create({
  id: "m-123",
  merchantGroup: SpotifyMerchantGroup.id,
  updatedAt: new Date().toISOString(),
});

const FlyeFitMerchant = MerchantModel.create({
  id: "m-234",
  merchantGroup: FlyeFitMerchantGroup.id,
  updatedAt: new Date().toISOString(),
});

const AertricityMerchant = MerchantModel.create({
  id: "m-345",
  merchantGroup: AertricityMerchantGroup.id,
  updatedAt: new Date().toISOString(),
});

// Products
const SpotifyPremiumIndividual = ProductModel.create({
  id: "sp-001",
  merchant: SpotifyMerchant.id,
  name: "Premium Individual",
  description: "Ad-free music listening, offline playback, and on-demand playback for one account.",
  idealFor: ["Individuals"],
  pricingTiers: [
    {
      id: "pt-001",
      name: "Premium Individual Tier",
      cost: 11.99,
      currency: "EUR",
      description: null,
      idealFor: ["Individuals"],
      updatedAt: new Date().toISOString(),
    },
  ],
  updatedAt: new Date().toISOString(),
});
const FlyeFitRoaming = ProductModel.create({
  id: "ff-001",
  merchant: FlyeFitMerchant.id,
  name: "Roaming Membership",
  description: "Access to all FlyeFit gyms nationwide with flexible hours",
  idealFor: ["Fitness Enthusiasts", "Commuters"],
  pricingTiers: [
    {
      id: "pt-002",
      name: "Monthly Roaming",
      cost: 29.99,
      currency: "EUR",
      description: "Monthly rolling contract",
      idealFor: ["Flexible schedules"],
      updatedAt: new Date().toISOString(),
    },
  ],
  updatedAt: new Date().toISOString(),
});

const AertricityHomeEnergy = ProductModel.create({
  id: "ae-001",
  merchant: AertricityMerchant.id,
  name: "Home Energy Plan",
  description: "Combined electricity and gas for your home",
  idealFor: ["Homeowners", "Renters"],
  pricingTiers: [
    {
      id: "pt-003",
      name: "Standard Rate",
      cost: 120.00,
      currency: "EUR",
      description: "Monthly standard rate for combined utilities",
      idealFor: ["All households"],
      updatedAt: new Date().toISOString(),
    },
  ],
  updatedAt: new Date().toISOString(),
});

const SpotifyFamily = ProductModel.create({
  id: "sp-002",
  merchant: SpotifyMerchant.id,
  name: "Premium Family",
  description: "Premium for up to 6 family members living at the same address",
  idealFor: ["Families", "Households"],
  pricingTiers: [
    {
      id: "pt-004",
      name: "Family Plan",
      cost: 17.99,
      currency: "EUR",
      description: "Monthly family plan for up to 6 accounts",
      idealFor: ["Families"],
      updatedAt: new Date().toISOString(),
    },
  ],
  updatedAt: new Date().toISOString(),
});

// Link merchant groups to merchants
FlyeFitMerchant.setMerchantGroup(FlyeFitMerchantGroup.id)
AertricityMerchant.setMerchantGroup(AertricityMerchantGroup.id)
SpotifyMerchant.setMerchantGroup(SpotifyMerchantGroup.id)

// Link merchants to merchant groups
FlyeFitMerchantGroup.addMerchant(FlyeFitMerchant.id);
AertricityMerchantGroup.addMerchant(AertricityMerchant.id);
SpotifyMerchantGroup.addMerchant(SpotifyMerchant.id);

// Link products to merchants
FlyeFitMerchant.addSubscriptionProduct(FlyeFitRoaming.id);
AertricityMerchant.addSubscriptionProduct(AertricityHomeEnergy.id);
SpotifyMerchant.addSubscriptionProduct(SpotifyFamily.id);
SpotifyMerchant.addSubscriptionProduct(SpotifyPremiumIndividual.id);

//Link products to merchant groups
SpotifyPremiumIndividual.setMerchant(SpotifyMerchant.id)
SpotifyFamily.setMerchant(SpotifyMerchant.id)
FlyeFitRoaming.setMerchant(FlyeFitMerchant.id)
AertricityHomeEnergy.setMerchant(AertricityMerchant.id)


// Export test merchant groups array (if you have them defined)
export const TestMerchantGroups = [
  FlyeFitMerchantGroup,
  SpotifyMerchantGroup,
  AertricityMerchantGroup
];

// Export test merchants array
export const TestMerchants = [
  FlyeFitMerchant,
  AertricityMerchant,
  SpotifyMerchant
];

// Export test products array
export const TestProducts = [
  SpotifyPremiumIndividual,
  SpotifyFamily,
  FlyeFitRoaming,
  AertricityHomeEnergy,
];


// Export initialized entities
export {
  FlyeFitMerchantGroup,
  SpotifyMerchantGroup,
  AertricityMerchantGroup,
  SpotifyMerchant,
  FlyeFitMerchant,
  AertricityMerchant,
  SpotifyPremiumIndividual,
  SpotifyFamily,
  FlyeFitRoaming,
  AertricityHomeEnergy,
};
