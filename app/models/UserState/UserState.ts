import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree"
import { withSetPropAction } from "../helpers/withSetPropAction"

/**
 * UserState model tracks user preferences and app state
 * including onboarding completion status
 */
export const UserStateModel = types
  .model("UserState")
  .props({
    hasCompletedOnboarding: types.optional(types.boolean, false)
  })
  .actions(withSetPropAction)
  .views((self) => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions((self) => ({
    completeOnboarding() {
      self.hasCompletedOnboarding = true
    }
  }))

export interface UserState extends Instance<typeof UserStateModel> {}
export interface UserStateSnapshotOut extends SnapshotOut<typeof UserStateModel> {}
export interface UserStateSnapshotIn extends SnapshotIn<typeof UserStateModel> {}
export const createUserStateDefaultModel = () => types.optional(UserStateModel, {
  hasCompletedOnboarding: false
})
