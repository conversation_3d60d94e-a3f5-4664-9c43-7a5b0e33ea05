import { UserStateModel } from "./UserState"

test("can be created", () => {
  const instance = UserStateModel.create({})

  expect(instance).toBeTruthy()
})

test("has default onboarding status as false", () => {
  const instance = UserStateModel.create({})

  expect(instance.hasCompletedOnboarding).toBe(false)
})

test("can set onboarding as completed", () => {
  const instance = UserStateModel.create({})

  instance.completeOnboarding()

  expect(instance.hasCompletedOnboarding).toBe(true)
})

test("can create with onboarding already completed", () => {
  const instance = UserStateModel.create({ hasCompletedOnboarding: true })

  expect(instance.hasCompletedOnboarding).toBe(true)
})
