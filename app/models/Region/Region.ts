import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree"
import { withSetPropAction } from "../helpers/withSetPropAction"

/**
 * Model description here for TypeScript hints.
 */
export const RegionModel = types
  .model("Region")
  .props({
      id: types.identifier,
      name: types.maybe(types.string),
      code: types.maybe(types.string)
  })
  .actions(withSetPropAction)
  .views((self) => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions((self) => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars

export interface Region extends Instance<typeof RegionModel> {}
export interface RegionSnapshotOut extends SnapshotOut<typeof RegionModel> {}
export interface RegionSnapshotIn extends SnapshotIn<typeof RegionModel> {}
// @ts-ignore
export const createRegionDefaultModel = () => types.optional(RegionModel, {})
