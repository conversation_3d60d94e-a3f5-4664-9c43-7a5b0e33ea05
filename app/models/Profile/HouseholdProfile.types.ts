import { Instance, SnapshotIn, SnapshotOut } from "mobx-state-tree";
import { HouseholdProfileModel } from "app/models/Profile/HouseholdProfile"; // Import the MODEL, not the instance

// Core interfaces (reusable across files)
export interface HouseholdProfile extends Instance<typeof HouseholdProfileModel> {}
export interface HouseholdProfileSnapshotOut extends SnapshotOut<typeof HouseholdProfileModel> {}
export interface HouseholdProfileSnapshotIn extends SnapshotIn<typeof HouseholdProfileModel> {}

// Optional: Type for descriptors (if they're more complex than strings)
export type HouseholdDescriptor = string; // Replace with a proper type if needed (e.g., object)

// Optional: Type for member operations (example)
export type AddMemberOptions = {
  firstName: string;
  lastName: string;
  descriptors?: HouseholdDescriptor[];
};