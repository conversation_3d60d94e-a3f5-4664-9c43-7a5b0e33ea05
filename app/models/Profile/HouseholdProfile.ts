import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree";
import { withSetPropAction } from "../helpers/withSetPropAction";
import { UserProfile, UserProfileModel } from "app/models/Profile/UserProfile";
import { v4 as uuidv4 } from "uuid";
import { runInAction } from "mobx";

// Define Descriptor type
const DescriptorModel = types.model({
  id: types.string,
  category: types.string,
  code: types.string,
  name: types.string,
  lastUpdated: types.Date
});

export const HouseholdProfileModel = types
  .model("HouseholdProfile")
  .props({
    id: types.identifier,
    name: types.string,
    members: types.array(UserProfileModel),
    descriptors: types.array(DescriptorModel),
    lastUpdated: types.Date
  })
  .actions(withSetPropAction)
  .actions((self) => ({
    getHousehold() {
      return self;
    },
    updateHouseholdName(name: string) {
      self.name = name;
    },
    updateHouseholdProfile(householdProfile: Instance<typeof HouseholdProfileModel>) {
      self.name = householdProfile.name;
      self.descriptors = householdProfile.descriptors;
      self.lastUpdated = new Date();
    },
    getUserProfileById(userId: string) {
      return self.members.find((x) => x.id === userId);
    },
    addMember(user: Instance<typeof UserProfileModel>) {
      self.members.push(user);
    },
    removeMember(user: Instance<typeof UserProfileModel>) {
      runInAction(() => {
        const index = self.members.findIndex(m => m?.id === user?.id)
        if (index >= 0) {
          self.members.splice(index, 1)
        }
      })
    },
    updateMember(user: Instance<typeof UserProfileModel>) {
      const index = self.members.findIndex((x) => x.id === user.id);
      if (index >= 0) {
        self.members[index] = user;
      } else {
        self.members.push(user);
      }
    },
    addNewMember(firstName: string, lastName: string) {
      const newMember = UserProfileModel.create({
        id: uuidv4(),
        firstName,
        lastName,
        householdId: self.id,
        descriptors: [],
        lastUpdated: new Date(),
      });
      self.members.push(newMember);
    }
  }));

// Create default household profile
export const householdProfile = HouseholdProfileModel.create({
  id: "XXX",
  name: "My Household",
  descriptors: [{
    id: "code_ABC",
    category: "The Basics",
    code: "student_houseshare",
    name: "Student Houseshare",
    lastUpdated: new Date()
  }],
  lastUpdated: new Date(),
  members: []
});

// Helper to add test members
function addTestMember(id: string, firstName: string, lastName: string) {
  householdProfile.addMember(
    UserProfileModel.create({
      id,
      firstName,
      lastName,
      householdId: "XXX",
      descriptors: [{
        id: "code_ABC",
        category: "The Basics",
        code: "student",
        name: "Student",
        lastUpdated: new Date()
      }],
      lastUpdated: new Date(),
    })
  );
}

// Add initial test members
addTestMember("123", "John", "Doe");
addTestMember("1234", "Emma", "Doe");
addTestMember("12345", "Robert", "Doe");

// Type definitions
export interface HouseholdProfile extends Instance<typeof HouseholdProfileModel> {}
export interface HouseholdProfileSnapshotOut extends SnapshotOut<typeof HouseholdProfileModel> {}
export interface HouseholdProfileSnapshotIn extends SnapshotIn<typeof HouseholdProfileModel> {}

export const createHouseholdProfileDefaultModel = () => 
  types.optional(HouseholdProfileModel, {
    id: "default-id",
    name: "Default Household",
    members: [],
    descriptors: [],
    lastUpdated: new Date(),
  });