import { Instance, types } from "mobx-state-tree";
import { ProfileDescriptorModel } from "app/models/Profile/ProfileDescriptor";
import { HouseholdProfile } from "./HouseholdProfile.types"; // Replace the moved interfaces



export const UserProfileModel = types
  .model("UserProfile", {
    id: types.identifier,
    avatarUrl: types.maybe(types.string),
    firstName: types.string,
    lastName: types.maybe(types.string),
    householdId: types.string,
    descriptors: types.array(ProfileDescriptorModel),
    lastUpdated: types.Date
  });
export interface UserProfile extends Instance<typeof UserProfileModel> {}
