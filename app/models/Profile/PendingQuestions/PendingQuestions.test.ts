import { ProfileQuestionModel } from "app/models/Profile/ProfileQuestion"
import { PendingQuestionsStoreModel } from "app/models"

// A mock question for testing purposes
const mockQuestion1 = ProfileQuestionModel.create({
  questionId: "q1",
  questionText: "What is your name?",
  allowMultiSelect: false,
  options: [],
  userOrHousehold: "user",
  targetId: "1",
})

const mockQuestion2 = ProfileQuestionModel.create({
  questionId: "q2",
  questionText: "What is your age?",
  allowMultiSelect: false,
  options: [],
  userOrHousehold: "user",
  targetId: "1",
})

describe("PendingQuestionsStore", () => {
  let pendingQuestionsStore: typeof PendingQuestionsStoreModel.Type

  beforeEach(() => {
    // Create a fresh instance of the store for each test
    pendingQuestionsStore = PendingQuestionsStoreModel.create()
  })

  it("should initialize with an empty pendingQuestions map", () => {
    expect(pendingQuestionsStore.pendingQuestions.size).toBe(0)
  })

  it("should add (enqueue) questions to a specific user/target queue", () => {
    pendingQuestionsStore.addPendingQuestion(mockQuestion1, "user", "1")
    const queue = pendingQuestionsStore.listPendingQuestions("user", "1")
    expect(queue).toHaveLength(1)
    expect(queue[0].questionId).toBe("q1")
  })

  it("should add multiple questions to the correct queue", () => {
    pendingQuestionsStore.addPendingQuestion(mockQuestion1, "user", "1")
    pendingQuestionsStore.addPendingQuestion(mockQuestion2, "user", "1")

    const queue = pendingQuestionsStore.listPendingQuestions("user", "1")
    expect(queue).toHaveLength(2)
    expect(queue[0].questionId).toBe("q1") // Oldest added question
    expect(queue[1].questionId).toBe("q2") // Most recent added question
  })

  it("should add questions to different queues based on user/targetId", () => {
    pendingQuestionsStore.addPendingQuestion(mockQuestion1, "user", "1")
    pendingQuestionsStore.addPendingQuestion(mockQuestion2, "household", "2")

    const userQueue = pendingQuestionsStore.listPendingQuestions("user", "1")
    const householdQueue = pendingQuestionsStore.listPendingQuestions(
      "household",
      "2",
    )

    expect(userQueue).toHaveLength(1)
    expect(userQueue[0].questionId).toBe("q1")

    expect(householdQueue).toHaveLength(1)
    expect(householdQueue[0].questionId).toBe("q2")
  })

  it("should add questions to the front of the queue", () => {
    pendingQuestionsStore.addPendingQuestion(mockQuestion1, "user", "1")
    pendingQuestionsStore.addPendingQuestionToFront(mockQuestion2, "user", "1")

    const queue = pendingQuestionsStore.listPendingQuestions("user", "1")
    expect(queue).toHaveLength(2)
    expect(queue[0].questionId).toBe("q2") // Question added to the front
    expect(queue[1].questionId).toBe("q1")
  })

  it("should remove (dequeue) the oldest question from a queue", () => {
    pendingQuestionsStore.addPendingQuestion(mockQuestion1, "user", "1")
    pendingQuestionsStore.addPendingQuestion(mockQuestion2, "user", "1")

    const removed = pendingQuestionsStore.removeOldestPendingQuestion(
      "user",
      "1",
    )
    expect(removed?.questionId).toBe("q1") // The oldest question

    const queue = pendingQuestionsStore.listPendingQuestions("user", "1")
    expect(queue).toHaveLength(1)
    expect(queue[0].questionId).toBe("q2") // Remaining question
  })

  it("should remove (dequeue) the newest question from a queue", () => {
    pendingQuestionsStore.addPendingQuestion(mockQuestion1, "user", "1")
    pendingQuestionsStore.addPendingQuestion(mockQuestion2, "user", "1")

    const removed = pendingQuestionsStore.removeNewestPendingQuestion(
      "user",
      "1",
    )
    expect(removed?.questionId).toBe("q2") // The newest question

    const queue = pendingQuestionsStore.listPendingQuestions("user", "1")
    expect(queue).toHaveLength(1)
    expect(queue[0].questionId).toBe("q1") // Remaining question
  })

  it("should clear all pending questions for a given user/targetId", () => {
    pendingQuestionsStore.addPendingQuestion(mockQuestion1, "user", "1")
    pendingQuestionsStore.addPendingQuestion(mockQuestion2, "user", "1")
    expect(pendingQuestionsStore.listPendingQuestions("user", "1")).toHaveLength(
      2,
    )

    pendingQuestionsStore.clearPendingQuestions("user", "1")
    expect(pendingQuestionsStore.listPendingQuestions("user", "1")).toHaveLength(
      0,
    )
  })

  it("should clear all pending questions for all users/targets", () => {
    pendingQuestionsStore.addPendingQuestion(mockQuestion1, "user", "1")
    pendingQuestionsStore.addPendingQuestion(mockQuestion2, "household", "2")

    expect(pendingQuestionsStore.pendingQuestions.size).toBe(2)

    pendingQuestionsStore.clearAllPendingQuestions()
    expect(pendingQuestionsStore.pendingQuestions.size).toBe(0)
  })

  it("should return the correct count of pending questions per queue", () => {
    pendingQuestionsStore.addPendingQuestion(mockQuestion1, "user", "1")
    pendingQuestionsStore.addPendingQuestion(mockQuestion2, "user", "1")

    const count = pendingQuestionsStore.getPendingQuestionCount("user", "1")
    expect(count).toBe(2)
  })

  it("should return 0 when there are no questions in a queue", () => {
    const count = pendingQuestionsStore.getPendingQuestionCount("user", "1")
    expect(count).toBe(0)
  })

})
