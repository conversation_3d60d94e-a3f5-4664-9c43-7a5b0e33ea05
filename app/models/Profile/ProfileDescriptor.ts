import { Instance, types } from "mobx-state-tree";

export const ProfileDescriptorModel = types
  .model("ProfileDescriptor", {
    id: types.identifier,
    category: types.string,
    icon: types.maybe(types.string),
    code: types.string,
    name: types.string,
    lastUpdated: types.Date
  });
export interface ProfileDescriptor extends Instance<typeof ProfileDescriptorModel> {}

export type GroupedByCategory = {
  [category: string]: ProfileDescriptor[];
};

export const groupByCategory = (items: ProfileDescriptor[]): GroupedByCategory => {
  return items.reduce((acc, item) => {
    const { category } = item;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(item);
    return acc;
  }, {} as GroupedByCategory);
};
