import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";

export const Hero1 = (onboardingRef) => {
  const handleContinue = () => {
    onboardingRef?.current?.goToPage(8, true); // update index as needed
  };

  return {
    backgroundColor: "#121212",
    image: <View />, // required by swiper
    title: (
      <View style={styles.container}>
        <View style={styles.progressWrapper}>
          <View style={styles.progressDots}>
            {[...Array(3)].map((_, i) => (
              <View
                key={i}
                style={[styles.dot, i === 0 ? styles.activeDot : styles.inactiveDot]}
              />
            ))}
          </View>
        </View>
        <View style={styles.contentWrapper}>
          <Text style={styles.heroText}>
            Some not-so-great news,{"\n"}and some great news!
          </Text>
          <TouchableOpacity style={styles.button} onPress={handleContinue}>
            <Text style={styles.buttonText}>Continue</Text>
          </TouchableOpacity>
        </View>
      </View>
    ),
    subtitle: <View />, // required by swiper
  };
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: "flex-start",
  },
  progressWrapper: {
    alignItems: "center",
    marginTop: 24,
    marginBottom: 48,
  },
  progressDots: {
    flexDirection: "row",
    gap: 8,
  },
  dot: {
    width: 20,
    height: 4,
    borderRadius: 2,
  },
  activeDot: {
    backgroundColor: "#FFFFFF",
  },
  inactiveDot: {
    backgroundColor: "#444444",
  },
  contentWrapper: {
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
  },
  heroText: {
    fontSize: 22,
    color: "#FFFFFF",
    fontWeight: "700",
    textAlign: "center",
    marginBottom: 40,
  },
  button: {
    backgroundColor: "#3B82F6",
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 24,
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});