import React, { useState } from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";

export const Onboarding3 = () => {
  const [selectedOption, setSelectedOption] = useState(null);

  const ageOptions = [
    "Under 18",
    "18–24",
    "25–34",
    "35–44",
    "45–55",
    "56–64",
    "Over 64"
  ];

  const handleSelect = (option) => {
    setSelectedOption(option);
    // Save or process age selection here
  };

  return {
    backgroundColor: "#121212",
    image: <View />, // required by swiper
    title: (
      <View style={styles.container}>
        <View style={styles.innerWrapper}>
          <Text style={styles.heading}>
            How old are you?
          </Text>
          <Text style={styles.subtitle}>
            So that we can suggest the best savings.
          </Text>
          <View style={styles.buttonGroup}>
            {ageOptions.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.button, selectedOption === option && styles.selectedButton]}
                onPress={() => handleSelect(option)}
              >
                <Text style={styles.buttonText}>{option}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    ),
    subtitle: <View />,
  };
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
  },
  innerWrapper: {
    alignItems: "center",
    justifyContent: "flex-start",
  },
  heading: {
    fontSize: 26,
    fontWeight: "700",
    color: "#FFFFFF",
    textAlign: "center",
    marginBottom: 8,
    lineHeight: 34,
  },
  subtitle: {
    fontSize: 15,
    color: "#999999",
    textAlign: "center",
    marginBottom: 24,
  },
  buttonGroup: {
    width: "100%",
    gap: 12,
  },
  button: {
    backgroundColor: "#333333",
    paddingVertical: 16,
    borderRadius: 28,
    width: "100%",
    alignItems: "center",
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
  selectedButton: {
    backgroundColor: "#555555",
    borderColor: "#FFFFFF",
    borderWidth: 1,
  },
});
