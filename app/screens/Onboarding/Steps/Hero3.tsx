import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";

export const Hero3 = (onboardingRef) => {
  const handleContinue = () => {
    onboardingRef?.current?.goToPage(9, true); // update index as needed
  };

  return {
    backgroundColor: "#121212",
    image: <View />, // required by swiper
    title: (
      <View style={styles.container}>
        <View style={styles.progressWrapper}>
          <View style={styles.progressDots}>
            {[...Array(3)].map((_, i) => (
              <View
                key={i}
                style={[styles.dot, i === 2 ? styles.activeDot : styles.inactiveDot]}
              />
            ))}
          </View>
        </View>
        <View style={styles.contentWrapper}>
          <Text style={styles.paragraph}>The good news is that</Text>
          <Text style={styles.paragraph}>OneSub can help you get back:</Text>
          <Text style={styles.blueLargeText}>
            <Text style={styles.extraLargeNumber}>€28,741</Text>
          </Text>
          <Text style={styles.paragraph}>over your lifetime on subscriptions.</Text>
          <Text style={styles.paragraph}>Imagine!</Text>
          <Text style={styles.caption}>According to your profile and the OneSub program.</Text>
          <TouchableOpacity style={styles.button} onPress={handleContinue}>
            <Text style={styles.buttonText}>Continue</Text>
          </TouchableOpacity>
        </View>
      </View>
    ),
    subtitle: <View />, // required by swiper
  };
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: "flex-start",
  },
  progressWrapper: {
    alignItems: "center",
    marginTop: 24,
    marginBottom: 48,
  },
  progressDots: {
    flexDirection: "row",
    gap: 8,
  },
  dot: {
    width: 20,
    height: 4,
    borderRadius: 2,
  },
  activeDot: {
    backgroundColor: "#FFFFFF",
  },
  inactiveDot: {
    backgroundColor: "#444444",
  },
  contentWrapper: {
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
  },
  paragraph: {
    color: "#FFFFFF",
    fontSize: 20,
    textAlign: "center",
    marginBottom: 12,
  },
  emphasis: {
    color: "#3B82F6",
    fontWeight: "600",
  },
  bigNumber: {
    color: "#3B82F6",
    fontSize: 32,
    fontWeight: "700",
    marginBottom: 12,
  },
  blueLargeText: {
    flexDirection: "row",
    textAlign: "center",
    marginBottom: 12,
  },
  largeNumber: {
    fontSize: 36,
    fontWeight: "600",
    color: "#3B82F6",
  },
  extraLargeNumber: {
    fontSize: 48,
    fontWeight: "700",
    color: "#3B82F6",
  },
  caption: {
    color: "#AAAAAA",
    fontSize: 12,
    textAlign: "center",
    marginTop: 24,
  },
  button: {
    marginTop: 32,
    backgroundColor: "#3B82F6",
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 24,
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});
