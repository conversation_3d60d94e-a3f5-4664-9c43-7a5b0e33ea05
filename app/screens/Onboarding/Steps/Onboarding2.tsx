import React, { useState } from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";

export const Onboarding2 = () => {
  const [selectedOption, setSelectedOption] = useState(null);

  const spendOptions = [
    "Less than €20/month",
    "€20–50",
    "€50–70",
    "€70–100",
    "€100+",
    "I'm not sure - help me calculate!"
  ];

  const handleSelect = (option) => {
    setSelectedOption(option);
    // Store or process this data as needed
  };

  return {
    backgroundColor: "#121212",
    image: <View />, // required by swiper
    title: (
      <View style={styles.container}>
        <View style={styles.innerWrapper}>
          <Text style={styles.heading}>
            What is your monthly{"\n"}spend on Subscriptions? <Text style={styles.emoji}>💸</Text>
          </Text>
          <Text style={styles.subtitle}>
            Let's find ways to save.
          </Text>
          <View style={styles.buttonGroup}>
            {spendOptions.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.button, selectedOption === option && styles.selectedButton]}
                onPress={() => handleSelect(option)}
              >
                <Text style={styles.buttonText}>{option}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    ),
    subtitle: <View />,
  };
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
  },
  innerWrapper: {
    alignItems: "center",
    justifyContent: "flex-start",
  },
  heading: {
    fontSize: 26,
    fontWeight: "700",
    color: "#FFFFFF",
    textAlign: "center",
    marginBottom: 8,
    lineHeight: 34,
  },
  emoji: {
    fontSize: 24,
  },
  subtitle: {
    fontSize: 15,
    color: "#999999",
    textAlign: "center",
    marginBottom: 24,
  },
  buttonGroup: {
    width: "100%",
    gap: 12,
  },
  button: {
    backgroundColor: "#333333",
    paddingVertical: 16,
    borderRadius: 28,
    width: "100%",
    alignItems: "center",
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
  selectedButton: {
    backgroundColor: "#555555",
    borderColor: "#FFFFFF",
    borderWidth: 1,
  },
});
