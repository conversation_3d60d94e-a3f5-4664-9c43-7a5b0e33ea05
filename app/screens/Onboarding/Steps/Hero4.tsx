import React from "react"
import { View, Text, StyleSheet, TouchableOpacity, Vibration } from "react-native"

export const Hero4 = (onboardingRef) => {
  const handleFistBump = () => {
    Vibration.vibrate(100) // vibrate for 100 milliseconds
    onboardingRef?.current?.goToPage(10, true) // go to CelebrateScreen (update index if needed)
  }

  return {
    backgroundColor: "#121212",
    image: <View />, // required by swiper
    title: (
      <View style={styles.container}>
        <View style={styles.checklist}>
          <Text style={styles.checkItem}>✅   Get clarity on your subscriptions</Text>
          <Text style={styles.checkItem}>✅   Find easy wins</Text>
          <Text style={styles.checkItem}>✅   Stay in control, forever</Text>
        </View>

        <View style={styles.fistSection}>
          <Text style={styles.fistText}>Solidify it with a fist bump</Text>

          <TouchableOpacity onPress={handleFistBump} style={styles.fistButton}>
            <Text style={styles.fistEmoji}>👊</Text>
          </TouchableOpacity>

          <Text style={styles.tapToContinue}>Tap to continue</Text>
        </View>
      </View>
    ),
    subtitle: <View />, // required by swiper
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  checklist: {
    marginBottom: 40,
  },
  checkItem: {
    fontSize: 18,
    color: "#FFFFFF",
    textAlign: "center",
    marginVertical: 8,
  },
  fistSection: {
    alignItems: "center",
  },
  fistText: {
    fontSize: 20,
    color: "#FFFFFF",
    marginBottom: 16,
    textAlign: "center",
  },
  fistButton: {
    backgroundColor: "#3B82F6",
    padding: 20,
    borderRadius: 50,
    marginBottom: 12,
  },
  fistEmoji: {
    fontSize: 36,
  },
  tapToContinue: {
    fontSize: 14,
    color: "#AAAAAA",
    textAlign: "center",
  },
})
