import React from "react";
import { View, ViewStyle, TextStyle, ImageStyle, TouchableOpacity } from "react-native";
import { Text } from "../../../components/Text";
import { AutoImage } from "../../../components/AutoImage";
import { spacing } from "../../../theme";

export const LandingPage = (onboardingRef) => {
  const handleGetStarted = () => {
    onboardingRef?.current?.goNext();
  };

  return {
    backgroundColor: "#121212",
    image: <View />, // required by swiper
    title: (
      <View style={$contentContainer}>
        <View style={$headerContainer}>
          <AutoImage
            source={require("../../../../assets/images/welcome-shield.png")}
            style={$imageStyle}
          />
          <Text preset="heading" style={$titleStyle}>
            Welcome to OneSub
          </Text>
          <Text preset="formHelper" style={$subtitleStyle}>
            Starting today, let's manage your subscriptions{"\n"}and save money.
          </Text>
        </View>

        <View style={$buttonContainer}>
          <TouchableOpacity
            style={$primaryButtonStyle}
            onPress={handleGetStarted}
          >
            <Text style={$primaryButtonTextStyle}>Get Started</Text>
          </TouchableOpacity>
        </View>
      </View>
    ),
    subtitle: <View />,
  };
};

const $contentContainer: ViewStyle = {
  flex: 1,
  paddingHorizontal: 24,
  paddingTop: 60,
  justifyContent: "space-between",
}

const $headerContainer: ViewStyle = {
  alignItems: "center",
  flex: 1,
  justifyContent: "center",
}

const $imageStyle: ImageStyle = {
  width: 200,
  height: 200,
  resizeMode: "contain",
  marginBottom: spacing.md,
}

const $titleStyle: TextStyle = {
  fontSize: 28,
  fontWeight: "600",
  color: "#FFFFFF",
  textAlign: "center",
  marginBottom: spacing.xs,
}

const $subtitleStyle: TextStyle = {
  color: "#D0D5DD",
  fontSize: 16,
  textAlign: "center",
  lineHeight: 24,
  marginTop: spacing.xs,
}

const $buttonContainer: ViewStyle = {
  paddingBottom: 32,
}

const $primaryButtonStyle: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 32,
  paddingVertical: 16,
  alignItems: "center",
  justifyContent: "center",
}

const $primaryButtonTextStyle: TextStyle = {
  color: "#121212",
  fontSize: 16,
  fontWeight: "600",
}
