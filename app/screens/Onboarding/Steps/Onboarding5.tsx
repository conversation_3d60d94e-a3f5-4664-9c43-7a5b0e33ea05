import React from "react"
import { View, Text, StyleSheet, Animated } from "react-native"

export function Onboarding5(onboardingRef: any) {
  const progress = new Animated.Value(0)

  Animated.sequence([
    Animated.timing(progress, {
      toValue: 0.05,
      duration: 2300,
      useNativeDriver: false,
    }),
    Animated.timing(progress, {
      toValue: 0.6,
      duration: 3000,
      useNativeDriver: false,
    }),
    Animated.timing(progress, {
      toValue: 0.58,
      duration: 5000,
      useNativeDriver: false,
    }),
    Animated.timing(progress, {
      toValue: 1,
      duration: 2000,
      useNativeDriver: false,
    }),
  ]).start(({ finished }) => {
    if (finished && onboardingRef?.current) {
      onboardingRef.current.goNext()
    }
  })

  const widthInterpolate = progress.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 200],
  })

  return {
    backgroundColor: "#121212",
    image: <View />, // empty image placeholder
    title: (
      <View style={styles.container}>
        <Text style={styles.text}>Calculating....</Text>
        <View style={styles.progressBarBackground}>
          <Animated.View style={[styles.progressBarFill, { width: widthInterpolate }]} />
        </View>
      </View>
    ),
    subtitle: "",
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#121212",
  },
  text: {
    fontSize: 16,
    color: "#FFFFFF",
    marginBottom: 16,
  },
  progressBarBackground: {
    width: 200,
    height: 4,
    backgroundColor: "#333",
    borderRadius: 2,
  },
  progressBarFill: {
    height: 4,
    backgroundColor: "#9B5DE5",
    borderRadius: 2,
  },
})