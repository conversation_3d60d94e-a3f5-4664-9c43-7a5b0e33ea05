import React, { useState } from "react";
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from "react-native";

const { width: screenWidth } = Dimensions.get("window");

export const Onboarding4 = () => {
  const [selectedOptions, setSelectedOptions] = useState([]);

  const subscriptionTypes = [
    "🎬 Streaming (Netflix, Disney+, etc.)",
    "🎵 Music & Audio (Spotify, Audible, etc.)",
    "📰 News & Magazines (The Economist, etc.)",
    "🎮 Gaming (Xbox, PlayStation Plus)",
    "💻 Software & Services (Adobe, Dropbox, etc.)",
    "🍱 Food & Grocery (HelloFresh, Deliveroo, etc.)",
  ];

  const toggleOption = (option) => {
    setSelectedOptions((prev) =>
      prev.includes(option)
        ? prev.filter((item) => item !== option)
        : [...prev, option]
    );
  };

  return {
    backgroundColor: "#121212",
    image: <View />, // required by swiper
    title: (
      <View style={styles.container}>
        <Text style={styles.heading}>
          What subscription types{"\n"}matter most to you?
        </Text>
        <Text style={styles.subtitle}>
          Choose as many as you like, and we'll tailor your experience.
        </Text>
        <View style={styles.buttonGroup}>
          {subscriptionTypes.map((option, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.button, selectedOptions.includes(option) && styles.selectedButton]}
              onPress={() => toggleOption(option)}
            >
              <Text style={styles.buttonText} numberOfLines={2} ellipsizeMode="tail">{option}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    ),
    subtitle: <View />, // required by swiper
  };
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    justifyContent: "flex-start",
  },
  heading: {
    fontSize: 24,
    fontWeight: "700",
    color: "#FFFFFF",
    textAlign: "center",
    marginBottom: 8,
    lineHeight: 32,
  },
  subtitle: {
    fontSize: 14,
    color: "#999999",
    textAlign: "center",
    marginBottom: 24,
  },
  buttonGroup: {
    width: "100%",
    gap: 12,
    alignItems: "center",
  },
  button: {
    backgroundColor: "#333333",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 28,
    width: screenWidth - 48, // full width minus total horizontal padding
    alignItems: "flex-start",
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
    flexWrap: "wrap",
  },
  selectedButton: {
    backgroundColor: "#555555",
    borderColor: "#FFFFFF",
    borderWidth: 1,
  },
});
