import React, { FC, useMemo } from "react"
import { observer } from "mobx-react-lite"
import { Pressable, View, ViewStyle } from "react-native"
import { AppStackScreenProps, navigate } from "app/navigators"
import { ProfileIcon, Screen, Text } from "app/components"
import { householdProfile, useStores } from "app/models"
import { groupByCategory, ProfileDescriptor } from "app/models/Profile/ProfileDescriptor"
import { colors } from "app/theme"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faX } from "@fortawesome/free-solid-svg-icons"
import { UserProfile } from "app/models/Profile/UserProfile"

interface UserProfileScreenProps extends AppStackScreenProps<"UserProfile"> {
  userId: string
}

export const UserProfileScreen: FC<UserProfileScreenProps> = observer(function UserProfileScreen(props: UserProfileScreenProps) {
  const rootStore = useStores()
  // @ts-ignore
  const {userId} = props?.route?.params
  const user = useMemo(()=>{
    return householdProfile.getUserProfileById(userId)
    },[userId])
  const groupedDescriptors =  useMemo(()=>{
    if(!user || user.descriptors.length == 0){
      return {}
    }
    return groupByCategory(user?.descriptors as ProfileDescriptor[])
  },[userId])
  const profileQuestionDepth = useMemo(()=>{
    return rootStore.pendingQuestions.getPendingQuestionCount("user", userId)
  },[userId])

  const sectionRender = (profileDescriptorCategory: string) => {
    const descriptors = groupedDescriptors[profileDescriptorCategory]
   return (
     <View key={profileDescriptorCategory}>
       <Text preset={"subheading"}>{profileDescriptorCategory}</Text>
       <View style={$descriptorContainer}>
         {descriptors.map((descriptor: ProfileDescriptor) => (
           <Pressable style={$descriptorBadge} key={descriptor.id}>
             <Text>{descriptor.name}</Text>
             <FontAwesomeIcon size={8} icon={faX} />
           </Pressable>
         ))
         }
       </View>
     </View>
  )
  }

  return (
    <Screen style={$root} preset="scroll">
      <ProfileIcon fullName={true} user={user as UserProfile} />
      <Text preset={"formHelper"} size={"xxs"} style={{textAlign:"center"}}>Review your profile</Text>
      {profileQuestionDepth > 0 &&
        <Pressable onPress={()=>navigate("ProfileQuestionModal")}>
          <Text size={"xxs"} weight={"light"} style={{textAlign:"center"}}>Your profile has <Text size={"xxs"} weight={"bold"}>{profileQuestionDepth}</Text> questions outstanding</Text>
        </Pressable>
      }
      <View
        style={{margin:16, backgroundColor: "white", padding:24, borderRadius: 8}}
      >
        {Object.keys(groupedDescriptors).map(sectionRender)}
      </View>
    </Screen>
  )
})

const $root: ViewStyle = {
  flex: 1,
  marginTop:16,
}
const $descriptorContainer: ViewStyle = {
  flexDirection:"row",
  gap:8
}
const $descriptorBadge: ViewStyle ={
  backgroundColor:colors.palette.primary200,
  padding:4,
  borderRadius:8,
  flexDirection:"row",
  gap:4,
  alignItems:"center"
}
