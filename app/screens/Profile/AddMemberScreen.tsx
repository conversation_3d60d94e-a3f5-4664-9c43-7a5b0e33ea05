import React, { FC, useState } from "react"
import { observer } from "mobx-react-lite"
import { AppStackScreenProps } from "app/navigators"
import { Text } from "app/components"
import { useNavigation } from "@react-navigation/native"
import { StackNavigationProp } from "@react-navigation/stack"
import { householdProfile } from "app/models/Profile/HouseholdProfile"
import { View, TextInput, Button, StyleSheet } from 'react-native';

interface AddMemberScreenProps extends AppStackScreenProps<"AddMember"> {}

export const AddMemberScreen = observer(() => {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const navigation = useNavigation<StackNavigationProp<AppStackScreenProps>>();

  const handleAddMember = () => {
    if (firstName.trim() && lastName.trim()) {
      householdProfile.addNewMember(firstName, lastName);
      navigation.goBack();
    }
  };

  return (
  <View style={styles.container}>
   <View style={styles.formContainer}>
    <Text style={styles.title}>Add New Member To Household</Text>
    
    <TextInput
      style={styles.input}
      placeholder="First Name"
      value={firstName}
      onChangeText={setFirstName}
    />
    
    <TextInput
      style={styles.input}
      placeholder="Last Name"
      value={lastName}
      onChangeText={setLastName}
    />
    
    <Button 
      title="Add Member" 
      onPress={handleAddMember} 
      disabled={!firstName.trim() || !lastName.trim()}
    />
  </View>
  </View>
  );
})

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3, // For Android shadow
  },
  input: {
    borderWidth: 1,
    borderColor: '#000000', // Pure black border
    borderRadius: 8, // Slightly more rounded corners
    padding: 12, // More padding for better touch area
    marginBottom: 16,
    backgroundColor: 'white',
    fontSize: 16, // Better readable text size
    height: 48, // Consistent height
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
});
