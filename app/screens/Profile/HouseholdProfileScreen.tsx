import React, { FC } from "react"
import { observer } from "mobx-react-lite"
import { Alert, Pressable, TextStyle, View, ViewStyle } from "react-native"
// @ts-ignore
import { StackNavigationProp } from "@react-navigation/stack"
import { Screen, Text } from "app/components"
import { ProfileGrid } from "app/components/custom/ProfileGrid"
import { colors, spacing } from "app/theme"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faPencil, faPlus, faHome, faChartBar, faGear, faRocket, faMagicWandSparkles } from "@fortawesome/free-solid-svg-icons"
import { useNavigation } from "@react-navigation/native"
import { householdProfile } from "app/models/Profile/HouseholdProfile"
import { AppStackScreenProps } from "app/navigators/AppNavigator"
import { navigate } from "app/navigators"

interface HouseholdProfileScreenProps extends AppStackScreenProps<"HouseholdProfile"> {}

export const HouseholdProfileScreen: FC<HouseholdProfileScreenProps> = observer(function HouseholdProfileScreen() {
  const navigation = useNavigation<StackNavigationProp<AppStackScreenProps<"HouseholdProfile">["navigation"]>>();
  
  const household = householdProfile.getHousehold();

  const members = [...householdProfile.members];

  const collectName = () => {
    Alert.prompt(
      "Rename household",
      "",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Update",
          onPress: (name) => {
            if (name) {
              householdProfile.updateHouseholdName(name)
            }
          }
        }
      ], 
      "plain-text", 
      household.name
    )
  }

  return (
    <View style={$container}>
      <Screen style={$root} preset="scroll">
        <View style={$householdNameContainer}>
          <Text style={{textAlign:"center"}} size={"xl"}>{household.name}</Text>
          <Pressable onPress={collectName} style={$pressableIcon}>
            <FontAwesomeIcon size={12} color={colors.palette.primary300} icon={faPencil} />
          </Pressable>
        </View>
        
        <View style={$profileGrid}>
          <ProfileGrid data={members} />
          <Pressable 
            onPress={() => navigation.navigate('AddMember')} 
            style={$addNewButton}
          >
            <FontAwesomeIcon size={12} color={colors.palette.primary300} icon={faPlus} />
          </Pressable>
        </View>
      </Screen>

      {/* Bottom Navigation Bar */}
      <View style={$navBar}>
        <Pressable style={$navButton} onPress={() => navigate("Welcome")}>
          <FontAwesomeIcon icon={faHome} size={24} color={colors.text} />
          <Text style={$navText}>Home</Text>
        </Pressable>
        <Pressable style={$navButton} onPress={() => navigate("UserSubscriptions")}>
          <FontAwesomeIcon icon={faRocket} size={24} color={colors.text} />
          <Text style={$navText}>Subscriptions</Text>
        </Pressable>
        <Pressable style={$navButton} onPress={() => navigate("UserAnalytics")}>
          <FontAwesomeIcon icon={faChartBar} size={24} color={colors.text} />
          <Text style={$navText}>Analytics</Text>
        </Pressable>
        <Pressable style={$navButton} onPress={() => navigate("UserSuggestions")}>
          <FontAwesomeIcon icon={faMagicWandSparkles} size={24} color="purple" />
          <Text style={$navText}>Suggestions</Text>
        </Pressable>
        <Pressable style={$navButton} onPress={() => navigate("UserSettings")}>
          <FontAwesomeIcon icon={faGear} size={24} color={colors.text} />
          <Text style={$navText}>Settings</Text>
        </Pressable>
      </View>
    </View>
  )
})

const $container: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $root: ViewStyle = {
  flex: 1,
  padding: 16,
  borderRadius: 16,
  backgroundColor: colors.background,
}

const $profileGrid: ViewStyle = {
  backgroundColor: "white",
  marginTop: 16,
  borderRadius: 16,
  padding: 16
}

const $householdNameContainer: ViewStyle = {
  flexDirection: "row", 
  gap: 8, 
  justifyContent: "center", 
  alignItems: "center"
}

const $pressableIcon: ViewStyle = {
  borderColor: colors.palette.primary300,
  borderWidth: 1,
  padding: 4, 
  borderRadius: 8
}

const $addNewButton: ViewStyle = {
  marginTop: 16,
  alignItems: "center",
  alignSelf: "center",
  marginLeft: 'auto',
  borderColor: colors.palette.primary300,
  borderWidth: 1,
  padding: 8, 
  borderRadius: 8
}

const $navBar: ViewStyle = {
  flexDirection: 'row',
  justifyContent: 'space-around',
  alignItems: 'center',
  backgroundColor: 'white',
  borderTopWidth: 0.5,
  borderTopColor: colors.palette.neutral300,
  paddingVertical: spacing.sm,
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  elevation: 5,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: -2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
}

const $navButton: ViewStyle = {
  alignItems: 'center',
  justifyContent: 'center',
  padding: spacing.xs,
}

const $navText: TextStyle = {
  fontSize: 12,
  color: colors.text,
  marginTop: spacing.xxs,
}
