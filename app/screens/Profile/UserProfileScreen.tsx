import React, { FC, useMemo, useState, useEffect } from "react"
import { observer } from "mobx-react-lite"
import { Pressable, View, ViewStyle, Alert, ActivityIndicator } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { ProfileIcon, Screen, Text } from "app/components"
import { useStores } from "app/models"
import { householdProfile } from "app/models/Profile/HouseholdProfile"
import { groupByCategory, ProfileDescriptor } from "app/models/Profile/ProfileDescriptor"
import { colors } from "app/theme"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faX, faTrash } from "@fortawesome/free-solid-svg-icons"
import { Instance } from "mobx-state-tree"
import { UserProfileModel } from "app/models/Profile/UserProfile"
import { runInAction } from "mobx"

type UserProfileScreenProps = AppStackScreenProps<"UserProfile">

export const UserProfileScreen: FC<UserProfileScreenProps> = observer(function UserProfileScreen(
  { navigation, route }
) {
  const rootStore = useStores()
  const userId = route.params?.userId
  const [showDeleteButton, setShowDeleteButton] = useState(false)
  const [deleting, setDeleting] = useState(false)

  // Safely get user with null checks
  const user = householdProfile.getUserProfileById(userId) as Instance<typeof UserProfileModel> | undefined

  useEffect(() => {
    // Clean up on screen blur
    return navigation.addListener('blur', () => {
      setShowDeleteButton(false)
      setDeleting(false)
    })
  }, [navigation])

  const handleLongPress = () => {
    if (!deleting) {
      setShowDeleteButton(true)
    }
  }

  const confirmDelete = () => {
    if (!user || deleting) return
    
    Alert.alert(
      "Confirm Delete",
      `This will permanently remove ${user.firstName} ${user.lastName} from your household?`,
      [
        {
          text: "Cancel",
          style: "cancel",
          onPress: () => setShowDeleteButton(false)
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            setDeleting(true)
            navigation.goBack()
            
            await new Promise(resolve => setTimeout(resolve, 100))
            
            runInAction(() => {
              try {
                householdProfile.removeMember(user)
              } catch (error) {
                console.error("Delete failed:", error)
              }
            })
          }
        }
      ]
    )
  }

  if (!user) {
    return (
      <Screen style={$root}>
        <Text>User not found</Text>
      </Screen>
    )
  }

  const groupedDescriptors = useMemo(() => {
    if (!user || user.descriptors.length === 0) return {}
    return groupByCategory(user.descriptors as ProfileDescriptor[])
  }, [user?.descriptors])

  const profileQuestionDepth = useMemo(() => {
    return rootStore.pendingQuestions.getPendingQuestionCount("user", userId)
  }, [userId, rootStore.pendingQuestions])

  const sectionRender = (profileDescriptorCategory: string) => {
    const descriptors = groupedDescriptors[profileDescriptorCategory]
    if (!descriptors) return null
    
    return (
      <View key={profileDescriptorCategory}>
        <Text preset={"subheading"}>{profileDescriptorCategory}</Text>
        <View style={$descriptorContainer}>
          {descriptors.map((descriptor: ProfileDescriptor) => (
            <Pressable 
              style={$descriptorBadge} 
              key={descriptor?.id || Math.random().toString()}
            >
              <Text>{descriptor.name}</Text>
              <FontAwesomeIcon size={8} icon={faX} />
            </Pressable>
          ))}
        </View>
      </View>
    )
  }

  return (
    <Screen style={$root} preset="scroll">
      <Pressable onLongPress={handleLongPress}>
        <ProfileIcon fullName={true} user={user} />
        <Text preset={"formHelper"} size={"xxs"} style={{ textAlign: "center" }}>
          Review your profile
        </Text>
        {profileQuestionDepth > 0 && (
          <Pressable onPress={() => navigation.navigate("ProfileQuestionModal")}>
            <Text size={"xxs"} weight={"light"} style={{ textAlign: "center" }}>
              Your profile has <Text size={"xxs"} weight={"bold"}>{profileQuestionDepth}</Text> questions outstanding
            </Text>
          </Pressable>
        )}
      </Pressable>

      <View style={$contentContainer}>
        {Object.keys(groupedDescriptors).map(sectionRender)}
      </View>

      {showDeleteButton && (
        <Pressable 
          style={[
            $deleteButton, 
            deleting && $deleteButtonDisabled
          ]}
          onPress={confirmDelete}
          disabled={deleting}
        >
          {deleting ? (
            <ActivityIndicator color="white" />
          ) : (
            <>
              <FontAwesomeIcon icon={faTrash} color="white" />
              <Text style={{ color: "white", marginLeft: 8 }}>Delete Member</Text>
            </>
          )}
        </Pressable>
      )}
    </Screen>
  )
})

const $root: ViewStyle = {
  flex: 1,
  marginTop: 16,
}

const $contentContainer: ViewStyle = {
  margin: 16,
  backgroundColor: "white",
  padding: 24,
  borderRadius: 8
}

const $descriptorContainer: ViewStyle = {
  flexDirection: "row",
  gap: 8,
  flexWrap: "wrap"
}

const $descriptorBadge: ViewStyle = {
  backgroundColor: colors.palette.primary200,
  padding: 8,
  borderRadius: 8,
  flexDirection: "row",
  gap: 4,
  alignItems: "center",
  marginBottom: 8
}

const $deleteButton: ViewStyle = {
  alignSelf: "center",
  marginTop: 20,
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.error,
  padding: 12,
  borderRadius: 8,
}

const $deleteButtonDisabled: ViewStyle = {
  opacity: 0.6
}