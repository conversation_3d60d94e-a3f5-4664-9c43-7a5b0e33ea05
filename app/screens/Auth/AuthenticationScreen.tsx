import React, { <PERSON> } from "react"
import { observer } from "mobx-react-lite"
import {ImageStyle, TextStyle, View, ViewStyle, TouchableOpacity} from "react-native"
import {AutoImage, Button, Text} from "app/components"
import {spacing} from "app/theme";
import {StatusBar} from "expo-status-bar";
import { AuthOptions } from "app/components/Auth/AuthOptions"
import { useStores } from "app/models"
import { navigate } from "app/navigators"

interface AuthenticationScreenProps {}

// Traditional screen component for when used outside of swiper
export const AuthenticationScreen: FC<AuthenticationScreenProps> = observer(function AuthenticationScreen() {
  const { auth } = useStores()

  const handleGetStarted = () => {
    // Navigate to the onboarding flow
    auth.startRegistration()
  }

  return (
      <View style={$screenStyle}>
          <StatusBar style="light"/>
              <View style={$headerContainer}>
                  <AutoImage
                      source={require("../../../assets/images/welcome-shield.png")}
                      style={$imageStyle}
                  />
                  <Text preset="heading" style={$titleStyle}>
                      Welcome to OneSub
                  </Text>
                  <Text preset="formHelper" style={$subtitleStyle}>
                      Starting today, let's manage your subscriptions{"\n"}and save money.
                  </Text>
              </View>

              <View style={$buttonContainer}>
                  <Button
                      preset="filled"
                      text="Get Started"
                      style={$primaryButtonStyle}
                      textStyle={$primaryButtonTextStyle}
                      onPress={handleGetStarted}
                  />
                  <AuthOptions displayMode={"column"} />
              </View>
      </View>

  )
})

// Swiper-compatible version for use in onboarding flow
export const AuthenticationPage = observer((onboardingRef) => {
  const { auth } = useStores()

  const handleGetStarted = () => {
    // Move to next page in swiper
    onboardingRef?.current?.goNext()
  }

  return {
    backgroundColor: "#121212",
    image: <View />, // required by swiper
    title: (
      <View style={$swiperContentContainer}>
        <View style={$swiperHeaderContainer}>
          <AutoImage
            source={require("../../../assets/images/welcome-shield.png")}
            style={$swiperImageStyle}
          />
          <Text preset="heading" style={$swiperTitleStyle}>
            Welcome to OneSub
          </Text>
          <Text preset="formHelper" style={$swiperSubtitleStyle}>
            Starting today, let's manage your subscriptions{"\n"}and save money.
          </Text>
        </View>

        <View style={$swiperButtonContainer}>
          <TouchableOpacity
            style={$swiperPrimaryButtonStyle}
            onPress={handleGetStarted}
          >
            <Text style={$swiperPrimaryButtonTextStyle}>Get Started</Text>
          </TouchableOpacity>
          <AuthOptions displayMode={"column"} />
        </View>
      </View>
    ),
    subtitle: <View />,
  }
})


const $screenStyle: ViewStyle = {
    backgroundColor: "#121212",
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: spacing.xl,
}

const $headerContainer: ViewStyle = {
    alignItems: "center",
    marginBottom: spacing.xxl,
}

const $imageStyle: ImageStyle = {
    width: 200,
    height: 200,
    resizeMode: "contain",
    margin: spacing.md
}

const $titleStyle: TextStyle = {
    fontSize: 28,
    fontWeight: "600",
    color: "#FFFFFF",
    textAlign: "center",
    marginBottom: spacing.xs,
}

const $subtitleStyle: TextStyle = {
    color: "#D0D5DD",
    fontSize: 16,
    textAlign: "center",
    lineHeight: 24,
    marginTop: spacing.xs,
}

const $buttonContainer: ViewStyle = {
    marginTop: spacing.xl,
}

const $primaryButtonStyle: ViewStyle = {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    paddingVertical: 14,
    marginBottom: spacing.md,
}

const $primaryButtonTextStyle: TextStyle = {
    color: "#121212",
    fontSize: 16,
    fontWeight: "600",
}

// Swiper-specific styles
const $swiperContentContainer: ViewStyle = {
  flex: 1,
  paddingHorizontal: 24,
  paddingTop: 60,
  justifyContent: "space-between",
}

const $swiperHeaderContainer: ViewStyle = {
  alignItems: "center",
  flex: 1,
  justifyContent: "center",
}

const $swiperImageStyle: ImageStyle = {
  width: 200,
  height: 200,
  resizeMode: "contain",
  marginBottom: spacing.md,
}

const $swiperTitleStyle: TextStyle = {
  fontSize: 28,
  fontWeight: "600",
  color: "#FFFFFF",
  textAlign: "center",
  marginBottom: spacing.xs,
}

const $swiperSubtitleStyle: TextStyle = {
  color: "#D0D5DD",
  fontSize: 16,
  textAlign: "center",
  lineHeight: 24,
  marginTop: spacing.xs,
}

const $swiperButtonContainer: ViewStyle = {
  paddingBottom: 32,
}

const $swiperPrimaryButtonStyle: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 32,
  paddingVertical: 16,
  alignItems: "center",
  justifyContent: "center",
  marginBottom: spacing.md,
}

const $swiperPrimaryButtonTextStyle: TextStyle = {
  color: "#121212",
  fontSize: 16,
  fontWeight: "600",
}


