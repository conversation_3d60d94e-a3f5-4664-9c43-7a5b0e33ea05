import React, { <PERSON> } from "react"
import { observer } from "mobx-react-lite"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text } from "app/components"
// No FontAwesome needed
// No icons needed
import { colors, spacing } from "app/theme"
import { navigate } from "app/navigators"

interface UserSuggestionsScreenProps extends AppStackScreenProps<"UserSuggestions"> {}

export const UserSuggestionsScreen: FC<UserSuggestionsScreenProps> = observer(function UserSuggestionsScreen() {
  return (
    <Screen style={$root} preset="scroll" contentContainerStyle={$scrollContainer}>
      <Text text="userSuggestions" />
    </Screen>
  )
})

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $scrollContainer: ViewStyle = {
  padding: spacing.md,
  paddingBottom: 80, // Space for bottom tab bar
}
