import { observer } from "mobx-react-lite";
import React, {FC, useEffect} from "react";
import { Pressable, View, StyleSheet, FlatList, Image, ScrollView } from "react-native";
import { navigate } from "../navigators";
import { ListView, NotificationListItem, Text } from "app/components"
import { useStores } from "app/models";
import { useAuth } from "app/contexts/AuthContext"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faHome, faChartBar, faCog, faMagicWandSparkles, faRocket } from "@fortawesome/free-solid-svg-icons"
import { colors, spacing } from "app/theme";
import { useNavigation } from "@react-navigation/core";
import { AppStackParamList } from "../navigators"; // Adjust the path based on your project structure
import { StackNavigationProp } from "@react-navigation/stack";

interface WelcomeScreenProps {}

export const WelcomeScreen: FC<WelcomeScreenProps> = observer(function WelcomeScreen() {
  const rootStore = useStores();
  const { userId, authLoading } = useAuth()
  const isLoading = authLoading
  const navigation = useNavigation<StackNavigationProp<AppStackParamList>>();
  const {pendingQuestions} = useStores()

  useEffect(() => {
    pendingQuestions.updateFromServer()
  }, []);

  const currentQueueDepth = userId
    ? rootStore.pendingQuestions.getPendingQuestionCount("user", userId)
    : 0

  const activeSubscriptions = [
    { id: "ff-001", category:"Fitness", title: "FlyeFit", imageUrl: "https://pbs.twimg.com/profile_images/1410622449977376768/1oCQ6v2l_400x400.jpg" },
    { id: "sp-001", category:"Streaming", title: "Spotify", imageUrl: "https://m.media-amazon.com/images/I/51rttY7a+9L.png" },
    { id: "at-001", category:"Utilities", title: "Airtricity", imageUrl: "https://play-lh.googleusercontent.com/z0-xrFvgLOFWhdVS_-CKF1_QnfccRCJVXr-aNRVGFz9xzOSLeWjcVs7f9YCgjOQ2P84" },
  ];

  const hiPriorityNotifications = [
    {
      title: "Airtricity",
      description: "Airtricity are offering you 90% off per year",
      logo: "https://play-lh.googleusercontent.com/z0-xrFvgLOFWhdVS_-CKF1_QnfccRCJVXr-aNRVGFz9xzOSLeWjcVs7f9YCgjOQ2P84",
      sponsored: true,
    },
    {
      title: "FlyeFit: save €50 per year",
      description: "As an ACME Co employee, FlyeFit offers you a 50% discount on annual roaming memberships",
      logo: "https://pbs.twimg.com/profile_images/1410622449977376768/1oCQ6v2l_400x400.jpg",
    },
    {
      title: "Spotify: Save €200 per year",
      description: "3 members of your household have individual Spotify subscriptions. Switch to a family plan.",
      logo: "https://m.media-amazon.com/images/I/51rttY7a+9L.png",
    },
  ];

  const renderSubscriptionItem = ({ item }: { item: typeof activeSubscriptions[0] }) => (
    <Pressable onPress={()=>navigation.navigate('Product', {id: item.id})} style={styles.subscriptionItem}>
      <Image
        source={{ uri: item.imageUrl }}
        style={styles.subscriptionImage}
        resizeMode="contain"
      />
      <View style={styles.subscriptionTextContainer}>
        <Text style={styles.subscriptionTitle}>{item.title}</Text>
        <Text style={styles.subscriptionCategory}>{item.category}</Text>
      </View>
      <Text style={styles.subscriptionPrice}>€9.99</Text>
    </Pressable>
  );

  const renderNotificationsList = ({ item }: { item: typeof hiPriorityNotifications[0] }) => (
    <NotificationListItem
      title={item.title}
      description={item.description}
      logo={item.logo}
      sponsored={item.sponsored ?? false}
    />
  );

  if (isLoading) {
    return <Text>Loading...</Text>
  }

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* Notifications Container - Top */}
        <View style={styles.notificationsContainer}>
          {currentQueueDepth > 0 && (
            <Pressable onPress={() => navigate("ProfileQuestionModal")}>
              <Text size="xxs" weight="light" style={{ padding: 4, textAlign: "right" }}>
                Complete your profile by answering <Text size="xxs" weight="bold">{currentQueueDepth}</Text>{" "}
                question{currentQueueDepth > 1 && "s"}
              </Text>
            </Pressable>
          )}
          <ListView
            estimatedItemSize={95}
            contentContainerStyle={styles.notificationsListContent}
            renderItem={renderNotificationsList}
            data={hiPriorityNotifications}
          />
        </View>

        {/* Subscriptions Container - Bottom */}
        <View style={styles.subscriptionsOuterContainer}>
          <Text size="xxs" weight="light" style={{ textAlign: "right", marginBottom: spacing.sm }}>
            €220 per month
          </Text>
          <View style={styles.subscriptionsContainer}>
            <FlatList
              data={activeSubscriptions}
              renderItem={renderSubscriptionItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.subscriptionsListContent}
              scrollEnabled={false} // Since we're in a ScrollView
            />
          </View>
        </View>
      </ScrollView>

      {/* No custom navigation buttons needed - using bottom tabs */}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContainer: {
    padding: spacing.md,
    paddingBottom: 80, // Increased to account for bottom tab bar
  },
  notificationsContainer: {
    backgroundColor: "white",
    borderRadius: 8,
    borderWidth: 0.1,
    marginBottom: spacing.md,
    padding: spacing.sm,
    flex: 3,
  },
  notificationsListContent: {
    paddingBottom: spacing.sm,
  },
  subscriptionsOuterContainer: {
    backgroundColor: "white",
    borderRadius: 8,
    borderWidth: 0.1,
    padding: spacing.sm,
    marginBottom: spacing.md,
    flex: 7,
  },
  subscriptionsContainer: {
    marginTop: spacing.sm,
  },
  subscriptionsListContent: {
    paddingBottom: spacing.sm,
  },
  subscriptionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.palette.neutral100,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.sm,
  },
  subscriptionImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: spacing.md,
  },
  subscriptionTextContainer: {
    flex: 1,
  },
  subscriptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xxs,
  },
  subscriptionCategory: {
    fontSize: 14,
    color: colors.palette.neutral500,
  },
  subscriptionPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.tint,
  },
  // No custom button styles needed
});
