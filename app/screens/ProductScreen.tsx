import React, { FC, useMemo, useState } from "react"
import { observer } from "mobx-react-lite"
import { Pressable, TextStyle, View, ViewStyle } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text,Card } from "app/components"
import { colors, spacing } from "app/theme"
import { PricingTier, Product, useStores } from "app/models"
import { ProductHeader } from "app/components/custom/ProductHeader"
import { PricingTierCard } from "app/components/custom/PricingTierCard"


interface ProductScreenProps extends AppStackScreenProps<"Product"> {}

export const ProductScreen: FC<ProductScreenProps> = observer(function ProductScreen(props) {
  const store = useStores()
  const products = store.products
  // @ts-ignore
  const { id } = props.route.params
  // @ts-ignore
  const product: Product = useMemo(() => products?.find((p) => p.id === id), [products, id])
  const [selectedTier, setSelectedTier] = useState<string | null>(null)

  const handleTierSelect = (tierId: string) => {
    setSelectedTier(tierId === selectedTier ? null : tierId)
  }

  if (!product) {
    return (
      <Screen contentContainerStyle={$root} preset="scroll">
        <Text text="Product not found" preset="subheading" />
      </Screen>
    )
  }

  return (
    <Screen contentContainerStyle={$root} preset="scroll">
      <ProductHeader product={product} />
      <View style={$pricingContainer}>
        <Text preset="heading" text="Choose Your Plan" style={$sectionTitle} />
        {product.pricingTiers.map((tier: PricingTier) => (
          <PricingTierCard
            key={tier.id}
            tier={tier}
            isSelected={selectedTier === tier.id}
            onPress={() => handleTierSelect(tier.id)}
          />
        ))}
      </View>

      {selectedTier && (
        <Card
          style={$highlightCard}
          heading="Your Selection"
          content={`${product.pricingTiers.find(t => t.id === selectedTier)?.name} - €${product.pricingTiers.find(t => t.id === selectedTier)?.cost}`}
          FooterComponent={
            <Pressable style={$confirmButton}>
              <Text text={`Continue to ${product?.merchant?.name}`} style={$buttonText} />
            </Pressable>
          }
        />
      )}
    </Screen>
  )
})

const $root: ViewStyle = {
  padding: spacing.lg,
  backgroundColor: "white",
}

const $pricingContainer: ViewStyle = {
  marginBottom: spacing.xxl,
}

const $sectionTitle: TextStyle = {
  marginBottom: spacing.lg,
  color: colors.palette.primary600,
}

const $highlightCard: ViewStyle = {
  marginTop: spacing.lg,
  borderRadius: 16,
  backgroundColor: colors.palette.primary500,
  borderWidth: 2,
  borderColor: colors.palette.primary600,
  padding: spacing.lg,
}

const $confirmButton: ViewStyle = {
  backgroundColor: colors.palette.primary600,
  borderRadius: 8,
  padding: spacing.md,
  marginTop: spacing.lg,
  alignItems: 'center',
}

const $buttonText: TextStyle = {
  color: colors.palette.neutral100,
  fontWeight: 'bold',
}
