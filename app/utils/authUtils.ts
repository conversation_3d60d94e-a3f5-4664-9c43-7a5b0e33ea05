import { supabase } from "app/config/config.base"
import * as WebBrowser from "expo-web-browser"
import * as Linking from "expo-linking"
import Constants, { ExecutionEnvironment } from "expo-constants"
import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin'
import { getGoogleClientId } from "app/config/oauth"

// Google Sign-In configuration state
let isGoogleSignInConfigured = false

/**
 * Configure Google Sign-In with dynamic client ID from Supabase
 */
export const configureGoogleSignIn = async (): Promise<void> => {
  try {
    if (isGoogleSignInConfigured) {
      console.log('Google Sign-In already configured')
      return
    }

    console.log('Configuring Google Sign-In...')
    const clientId = await getGoogleClientId()

    GoogleSignin.configure({
      iosClientId: clientId,
      offlineAccess: false, // Set to false to avoid needing server web ClientID
      hostedDomain: '',
      forceCodeForRefreshToken: false, // Set to false for simpler setup
    })

    isGoogleSignInConfigured = true
    console.log('Google Sign-In configured successfully with client ID:', clientId)
  } catch (error) {
    console.error('Error configuring Google Sign-In:', error)
    throw error
  }
}

const getRedirectUrl = (): string => {
  try {
    let redirectURL

    // Check if we're in Expo Go environment
    if (Constants.executionEnvironment === ExecutionEnvironment.StoreClient) {
      // Expo Go: Use host laptop's details
      const hostUri = Constants.expoConfig?.hostUri || "localhost:19000"
      const ipAndPort = hostUri.split(":")[0] === "localhost" ? "*************:19000" : hostUri
      redirectURL = `exp://${ipAndPort}/auth/magic`
    } else {
      // Development build or production: Use custom URL scheme
      redirectURL = Linking.createURL("auth/magic")
    }

    console.log('Generated redirect URL:', redirectURL)
    return redirectURL
  } catch (error) {
    console.error('Error generating redirect URL:', error)
    // Fallback to a default URL scheme
    return "onesub://auth/magic"
  }
}

export const createSessionFromUrl = async (url: string) => {
    try {
        // Parse URL parameters manually to avoid expo-auth-session/build issues
        const urlObj = new URL(url);
        const params = new URLSearchParams(urlObj.hash.substring(1)); // Remove # and parse

        const access_token = params.get('access_token');
        const refresh_token = params.get('refresh_token');
        const error = params.get('error');
        const error_description = params.get('error_description');

        if (error) {
            throw new Error(error_description || error);
        }

        if (!access_token) {
            console.log('No access token found in URL:', url);
            return null;
        }

        const { data, error: sessionError } = await supabase.auth.setSession({
            access_token,
            refresh_token: refresh_token || '',
        });

        if (sessionError) throw sessionError;
        return data.session;
    } catch (error) {
        console.error('Error creating session from URL:', error);
        throw error;
    }
};

export const performOAuth = async (provider: "google" | "github" | "apple" | "facebook") => {
    // Create redirect URI manually to avoid expo-auth-session issues
    const redirectTo = "onesub://";

    console.log(`Starting OAuth for ${provider} with redirect URI: ${redirectTo}`);

    const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
            redirectTo,
            skipBrowserRedirect: true,
        },
    });

    if (error) {
        console.error(`OAuth error for ${provider}:`, error);
        throw error;
    }

    console.log(`Opening OAuth URL: ${data?.url}`);

    const res = await WebBrowser.openAuthSessionAsync(
        data?.url ?? "",
        redirectTo
    );

    console.log(`OAuth result:`, res);

    if (res.type === "success") {
        const { url } = res;
        console.log(`OAuth success, processing URL: ${url}`);
        const session = await createSessionFromUrl(url);
        return session;
    } else if (res.type === "cancel") {
        console.log("OAuth was cancelled by user");
        throw new Error("Authentication was cancelled");
    } else {
        console.log("OAuth failed or was dismissed");
        throw new Error("Authentication failed");
    }
};

export const sendMagicLink = async (email: string) => {
    try {
        console.log('Sending magic link to:', email)
        const redirectTo = getRedirectUrl()

        if (!redirectTo) {
            throw new Error('Failed to generate redirect URL')
        }

        const { error } = await supabase.auth.signInWithOtp({
            email: email,
            options: {
                emailRedirectTo: redirectTo,
            },
        });

        if (error) {
            console.error('Supabase magic link error:', error)
            throw error
        }

        console.log('Magic link sent successfully')
    } catch (error) {
        console.error('Error in sendMagicLink:', error)
        throw error
    }
};

export const performGoogleSignIn = async () => {
    try {
        console.log('performGoogleSignIn function called successfully');
        console.log('Starting Google Sign-In...');

        // Ensure Google Sign-In is configured with dynamic client ID
        await configureGoogleSignIn();

        // Check if device supports Google Play Services
        await GoogleSignin.hasPlayServices();

        // Sign in with Google
        const userInfo = await GoogleSignin.signIn();
        console.log('Google Sign-In successful:', userInfo);

        // Get the ID token
        const idToken = userInfo.data?.idToken;
        if (!idToken) {
            throw new Error('No ID token received from Google');
        }

        console.log('Got ID token, signing in with Supabase...');

        // Sign in with Supabase using the Google ID token
        const { data, error } = await supabase.auth.signInWithIdToken({
            provider: 'google',
            token: idToken,
        });

        if (error) {
            console.error('Supabase sign-in error:', error);
            throw error;
        }

        console.log('Supabase sign-in successful:', data);
        return data.session;

    } catch (error: any) {
        console.error('Google Sign-In error:', error);

        if (error.code === statusCodes.SIGN_IN_CANCELLED) {
            throw new Error('Google Sign-In was cancelled');
        } else if (error.code === statusCodes.IN_PROGRESS) {
            throw new Error('Google Sign-In is already in progress');
        } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
            throw new Error('Google Play Services not available');
        } else {
            throw error;
        }
    }
};
