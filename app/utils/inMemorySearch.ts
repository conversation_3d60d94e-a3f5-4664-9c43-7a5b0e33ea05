// app/utilities/search.ts
import { MerchantGroupModel, MerchantModel, ProductModel, RootStore, UserSubscriptionModel } from "app/models"
import { fuzzy } from "fast-fuzzy"
import { Instance } from "mobx-state-tree"

type SearchableEntity =
  | Instance<typeof UserSubscriptionModel>
  | Instance<typeof ProductModel>
  | Instance<typeof MerchantModel>
  | Instance<typeof MerchantGroupModel>

export type SearchResult = {
  type: 'subscription' | 'product' | 'merchant' | 'merchantGroup'
  entity: SearchableEntity
  score: number
}

const SEARCH_OPTIONS = {
  threshold: 0.7,
  ignoreCase: true,
  normalizeWhitespace: true,
  ignoreSymbols: true
}

// TODO: Recompute index periodically? Maybe after every refresh?
export const createSearchService = (store: RootStore) => {
  // Pre-cache search strings for performance
  const searchIndex = {
    subscriptions: store.subscriptions.map(sub => ({
      id: sub.id,
      text: `${sub.name} ${sub.description}`
    })),
    products: store.products.map(prod => ({
      id: prod.id,
      text: `${prod.name} ${prod.description}`
    })),
    merchants: store.merchants.map(m => ({
      id: m.id,
      text: `${m.name} ${m.description}`
    })),
    merchantGroups: store.merchantGroups.map(mg => ({
      id: mg.id,
      text: `${mg.name} ${mg.description}`
    }))
  }

  return {
    search(query: string): SearchResult[] {
      if (!query) return []

      const results: SearchResult[] = []

      // Search in priority order with individual thresholds
      const searchPhases = [
        {
          type: 'subscription' as const,
          items: store.subscriptions,
          index: searchIndex.subscriptions
        },
        {
          type: 'product' as const,
          items: store.products,
          index: searchIndex.products
        },
        {
          type: 'merchant' as const,
          items: store.merchants,
          index: searchIndex.merchants
        },
        {
          type: 'merchantGroup' as const,
          items: store.merchantGroups,
          index: searchIndex.merchantGroups
        }
      ]

      for (const phase of searchPhases) {
        phase.index.forEach((entry, index) => {
          const score = fuzzy(query, entry.text, SEARCH_OPTIONS)
          if (score >= SEARCH_OPTIONS.threshold) {
            results.push({
              type: phase.type,
              entity: phase.items[index],
              score
            })
          }
        })
      }

      return results.sort((a, b) => {
        // Priority first, then score
        const typeOrder = searchPhases.map(p => p.type)
        const aPriority = typeOrder.indexOf(a.type)
        const bPriority = typeOrder.indexOf(b.type)

        return aPriority - bPriority || b.score - a.score
      })
    }
  }
}
