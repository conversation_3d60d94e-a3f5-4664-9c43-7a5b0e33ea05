import { Platform } from "react-native"
import { 
  faGoogle, 
  faApple, 
  faFacebook,
} from "@fortawesome/free-brands-svg-icons"
import { faEnvelope } from "@fortawesome/free-solid-svg-icons"
import { colors } from "../theme"

export interface AuthProviderConfig {
  id: string
  name: string
  icon: any // FontAwesome icon
  provider: "google" | "apple" | "github" | "facebook" | "twitter" | "email"
  buttonPreset: "default" | "filled" | "reversed"
  buttonStyle?: {
    backgroundColor?: string
    textColor?: string
    iconColor?: string
    borderColor?: string
  }
  platformSpecific?: "ios" | "android" | "all"
}

export const authProviders: AuthProviderConfig[] = [
  {
    id: "apple",
    name: "Apple",
    icon: faApple,
    provider: "apple",
    buttonPreset: "reversed",
    buttonStyle: {
      backgroundColor: "#000000",
      textColor: "#FFFFFF",
      iconColor: "#FFFFFF"
    },
    platformSpecific: "ios"
  },
  {
    id: "google",
    name: "Google",
    icon: faGoogle,
    provider: "google",
    buttonPreset: "default",
    buttonStyle: {
      backgroundColor: "#ffffff",
      textColor: "#757575",
      iconColor: "#4285F4",
      borderColor: "#DADCE0"
    },
    platformSpecific: "all"
  },

  {
    id: "facebook",
    name: "Facebook",
    icon: faFacebook,
    provider: "facebook",
    buttonPreset: "filled",
    buttonStyle: {
      backgroundColor: "#1877F2",
      textColor: "#FFFFFF",
      iconColor: "#FFFFFF"
    },
    platformSpecific: "all"
  },
  {
    id: "email",
    name: "Login with Email",
    icon: faEnvelope,
    provider: "email",
    buttonPreset: "default",
    buttonStyle: {
      backgroundColor: colors.palette.neutral200,
      textColor: colors.text,
      iconColor: colors.text
    },
    platformSpecific: "all"
  }
]

export const getAvailableProviders = (): AuthProviderConfig[] => {
  return authProviders.filter(provider => {
    if (provider.platformSpecific === "all") return true
    if (provider.platformSpecific === "ios" && Platform.OS === "ios") return true
    if (provider.platformSpecific === "android" && Platform.OS === "android") return true
    return false
  })
}

export const getProviderById = (id: string): AuthProviderConfig | undefined => {
  return authProviders.find(provider => provider.id === id)
}
