import { authProviderService } from "app/services/AuthProviderService"

/**
 * OAuth configuration for different providers
 * Note: Google client ID is now fetched dynamically from Supabase
 */

export const oauthConfig = {
  google: {
    // Fallback client ID (kept for emergency fallback only)
    fallbackIosClientId: "1063372141663-35h1h0dofnsgi66esl1c1gc28a4ccfr2.apps.googleusercontent.com",
    // Add Android client ID when you create one
    androidClientId: "",
    // Add web client ID if needed (for Supabase configuration)
    webClientId: "",
  },
  // Add other OAuth providers here as needed
  apple: {
    // Apple Sign-In configuration
  },
  facebook: {
    // Facebook OAuth configuration
  }
}

/**
 * Get Google client ID dynamically from Supabase
 * Falls back to hardcoded value if Supabase is unavailable
 */
export const getGoogleClientId = async (): Promise<string> => {
  try {
    const clientId = await authProviderService.getGoogleClientId()

    if (clientId) {
      console.log('Using Google client ID from Supabase')
      return clientId
    }

    console.warn('No Google client ID found in Supabase, using fallback')
    return oauthConfig.google.fallbackIosClientId
  } catch (error) {
    console.error('Error fetching Google client ID, using fallback:', error)
    return oauthConfig.google.fallbackIosClientId
  }
}

/**
 * Generate reversed client ID for URL scheme
 */
export const getReversedClientId = (clientId: string): string => {
  // Remove .apps.googleusercontent.com and reverse the domain
  return clientId.replace('.apps.googleusercontent.com', '').split('.').reverse().join('.')
}
