import React from "react"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import * as Screens from "app/screens"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faChartLine, faCog, faHome, faLightbulb } from "@fortawesome/free-solid-svg-icons"
import { faCreditCard } from "@fortawesome/free-regular-svg-icons"
import { CustomHeaders } from "app/components"

/**
 * This type allows TypeScript to know what routes are defined in this navigator
 */
export type TabNavigatorParamList = {
  Welcome: undefined
  UserSubscriptions: undefined
  UserAnalytics: undefined
  UserSuggestions: undefined
  UserSettings: undefined
}

const Tab = createBottomTabNavigator<TabNavigatorParamList>()

export const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: "#000",
        tabBarInactiveTintColor: "#999",
      }}
      initialRouteName="UserSubscriptions"
    >
      {/*<Tab.Screen*/}
      {/*  name="Welcome"*/}
      {/*  component={Screens.WelcomeScreen}*/}
      {/*  options={{*/}
      {/*    title: "Home",*/}
      {/*    tabBarIcon: ({ color, size }) => (*/}
      {/*      <FontAwesomeIcon icon={faHome} color={color} size={size} />*/}
      {/*    ),*/}
      {/*  }}*/}
      {/*/>*/}
      <Tab.Screen
        name="UserSubscriptions"
        component={Screens.UserSubscriptionsScreen}
        options={{
          title: "Subscriptions",
          tabBarIcon: ({ color, size }) => (
            <FontAwesomeIcon icon={faCreditCard} color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="UserAnalytics"
        component={Screens.UserAnalyticsScreen}
        options={{
          title: "Analytics",
          tabBarIcon: ({ color, size }) => (
            <FontAwesomeIcon icon={faChartLine} color={color} size={size} />
          ),
        }}
      />
      {/*<Tab.Screen*/}
      {/*  name="UserSuggestions"*/}
      {/*  component={Screens.UserSuggestionsScreen}*/}
      {/*  options={{*/}
      {/*    title: "Suggestions",*/}
      {/*    tabBarIcon: ({ color, size }) => (*/}
      {/*      <FontAwesomeIcon icon={faLightbulb} color={color} size={size} />*/}
      {/*    ),*/}
      {/*  }}*/}
      {/*/>*/}
      <Tab.Screen
        name="UserSettings"
        component={Screens.UserSettingsScreen}
        options={{
          title: "Settings",
          tabBarIcon: ({ color, size }) => (
            <FontAwesomeIcon icon={faCog} color={color} size={size} />
          ),
        }}
      />
    </Tab.Navigator>
  )
}
