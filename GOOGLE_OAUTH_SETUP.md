# Google OAuth Setup for OneSub Mobile App

## Current Configuration

Your app is now configured to use **native Google Sign-In** with Supa<PERSON> and **dynamic client ID fetching**. The Google Client ID is now stored in Supabase and fetched dynamically at runtime.

### 1. OAuth Client ID Configuration
- **Primary Source**: Supabase `auth_provider_config` table
- **Current Client ID**: `1063372141663-35h1h0dofnsgi66esl1c1gc28a4ccfr2.apps.googleusercontent.com`
- **Fallback Client ID**: `1063372141663-35h1h0dofnsgi66esl1c1gc28a4ccfr2.apps.googleusercontent.com` (in `app/config/oauth.ts`)
- **Dynamic Fetching**: Implemented via `AuthProviderService`

### 2. Dynamic Configuration Features
- **Runtime Fetching**: Client ID is fetched from Supabase at app startup
- **Caching**: Client ID is cached in AsyncStorage for 24 hours
- **Fallback**: Hardcoded fallback client ID used if <PERSON><PERSON><PERSON> is unavailable
- **Error Handling**: Graceful degradation if network/Supabase issues occur

### 3. Dependencies Added
- **@react-native-google-signin/google-signin**: Native Google Sign-In SDK for React Native

### 3. App Configuration Updates
- Added Google Sign-In plugin to `app.json`
- Configured iOS URL scheme with your client ID
- Added proper iOS Info.plist configuration
- Configured URL scheme: `onesub://`

### 4. Code Implementation
- **Native Google Sign-In**: Uses `@react-native-google-signin/google-signin` for mobile
- **Supabase Integration**: Signs in with Supabase using Google ID token
- **Fallback OAuth**: Other providers still use web OAuth flow
- Added comprehensive error handling and logging

## Required Supabase Configuration

To complete the setup, you need to configure your Supabase project:

### 1. Add Google as OAuth Provider
1. Go to your Supabase Dashboard
2. Navigate to Authentication > Providers
3. Enable Google provider
4. Add your Google OAuth credentials:
   - **Client ID**: `1063372141663-35h1h0dofnsgi66esl1c1gc28a4ccfr2.apps.googleusercontent.com`
   - **Client Secret**: You'll need to create a **Web Application** OAuth client in Google Cloud Console for this

### 2. Create auth_provider_config Table
Create a table in Supabase to store OAuth client IDs:

```sql
CREATE TABLE auth_provider_config (
  id SERIAL PRIMARY KEY,
  provider VARCHAR(50) NOT NULL UNIQUE,
  client_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert Google client ID
INSERT INTO auth_provider_config (provider, client_id)
VALUES ('google', '1063372141663-35h1h0dofnsgi66esl1c1gc28a4ccfr2.apps.googleusercontent.com');

-- Enable RLS and allow read access for unauthenticated users
ALTER TABLE auth_provider_config ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow read access to auth provider config" ON auth_provider_config
FOR SELECT USING (true);
```

### 3. Important: Two OAuth Clients Needed
You need **TWO** OAuth clients in Google Cloud Console:

**1. iOS Application Client (Updated)**
- Type: iOS Application
- Client ID: `1063372141663-35h1h0dofnsgi66esl1c1gc28a4ccfr2.apps.googleusercontent.com`
- Bundle ID: `com.onesub.mobile`
- Used by: Mobile app for native sign-in

**2. Web Application Client (Need to Create)**
- Type: Web Application
- Used by: Supabase for server-side verification
- Authorized redirect URIs: `https://[your-supabase-project-ref].supabase.co/auth/v1/callback`

### 4. No Redirect URLs Needed for Mobile
Since we're using native Google Sign-In, you don't need to configure redirect URLs in Supabase for the mobile app. The native SDK handles the authentication flow directly.

## Implementation Details

### Dynamic Client ID Fetching
The app now fetches the Google Client ID from Supabase at runtime:

1. **App Startup**: `AuthProviderService` initializes and loads cached config
2. **Cache Check**: If cache is valid (< 24 hours), uses cached client ID
3. **Supabase Fetch**: If cache is invalid/empty, fetches from `auth_provider_config` table
4. **Fallback**: If Supabase is unavailable, uses hardcoded fallback client ID
5. **Google Configuration**: Client ID is used to configure Google Sign-In SDK

### Files Modified
- `app/services/AuthProviderService.ts` - New service for fetching auth configs
- `app/config/oauth.ts` - Updated to support dynamic client IDs
- `app/utils/authUtils.ts` - Added dynamic Google Sign-In configuration
- `app/contexts/AuthContext.tsx` - Added service initialization
- `app.json` - Updated URL scheme for new client ID

### Security Notes
- Only public OAuth client IDs are stored in Supabase (no secrets)
- RLS policy allows unauthenticated read access to `auth_provider_config`
- Fallback mechanism ensures app works even if Supabase is down
- Client ID is cached locally to reduce API calls

## Testing the Implementation

1. **Development Testing**: Run the app in Expo Go and test Google login
2. **Production Testing**: Build a standalone app and test the native Google Sign-In flow

## Troubleshooting

### Fixed Issues:
1. **"Missing OAuth secret" (400 error)**: ✅ Fixed by using native Google Sign-In instead of web OAuth
2. **"Invalid redirect URI"**: ✅ Not applicable with native sign-in

### Potential Issues:
1. **"Google Play Services not available"**: Only affects Android devices without Google Play Services
2. **"Sign-in cancelled"**: Normal user behavior, no error shown
3. **"No ID token received"**: Check Google Cloud Console configuration

### Debug Logs:
The implementation includes comprehensive console logs:
- Google Sign-In start/completion
- ID token retrieval
- Supabase authentication
- Error details with specific error codes

### Error Codes:
- `SIGN_IN_CANCELLED`: User cancelled the sign-in
- `IN_PROGRESS`: Sign-in already in progress
- `PLAY_SERVICES_NOT_AVAILABLE`: Google Play Services not available (Android)

## Next Steps

1. Configure your Supabase project with Google OAuth
2. Test the Google login button in your app
3. Verify the authentication flow works end-to-end
4. Consider adding Android OAuth client ID when needed

## Files Modified

- `app/utils/authUtils.ts` - Added Google OAuth support
- `app/components/Auth/AuthOptions.tsx` - Updated OAuth handling
- `app/config/oauth.ts` - Added OAuth configuration
- `app.json` - Added expo-auth-session plugin
- `GOOGLE_OAUTH_SETUP.md` - This documentation
